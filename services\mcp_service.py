"""
MCP (Model Context Protocol) Service for Figma Integration.

This service manages the connection to the Figma MCP server and provides
tools for real-time Figma synchronization following OpenAI SDK patterns.
"""

import asyncio
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from config.settings import settings

logger = logging.getLogger(__name__)


class MCPService:
    """Service for managing MCP server connections and Figma integration."""
    
    def __init__(self):
        self.figma_server = None
        self.server_process = None
        self.is_connected = False
        self.available_tools: List[Dict[str, Any]] = []
        
    async def initialize(self) -> bool:
        """Initialize the MCP service and start the Figma MCP server."""
        try:
            logger.info("🔌 Initializing MCP Service...")
            
            # Start the Figma MCP server
            await self._start_figma_server()
            
            # Connect to the server
            await self._connect_to_server()
            
            # List available tools
            await self._refresh_tools()
            
            logger.info(f"✅ MCP Service initialized with {len(self.available_tools)} tools")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize MCP Service: {e}")
            return False
    
    async def _start_figma_server(self):
        """Start the Figma MCP server using Bun."""
        try:
            server_path = Path(settings.mcp_server_path)
            if not server_path.exists():
                raise FileNotFoundError(f"MCP server not found at: {server_path}")
            
            logger.info(f"🚀 Starting Figma MCP server: {server_path}")
            
            # Start the Bun-based TypeScript server
            self.server_process = subprocess.Popen([
                "bun", "run", str(server_path)
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait a moment for the server to start
            await asyncio.sleep(2)
            
            # Check if the process is still running
            if self.server_process.poll() is not None:
                stdout, stderr = self.server_process.communicate()
                raise RuntimeError(f"MCP server failed to start: {stderr.decode()}")
            
            logger.info("✅ Figma MCP server started successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to start Figma MCP server: {e}")
            raise
    
    async def _connect_to_server(self):
        """Connect to the MCP server using stdio transport."""
        try:
            # Import MCP classes (will be available after installing openai-agents)
            from agents.mcp.server import MCPServerStdio
            
            # Create MCP server connection
            self.figma_server = MCPServerStdio(
                params={
                    "command": "bun",
                    "args": ["run", settings.mcp_server_path]
                },
                cache_tools_list=True  # Cache tools for better performance
            )
            
            # Initialize the connection
            await self.figma_server.__aenter__()
            self.is_connected = True
            
            logger.info("✅ Connected to Figma MCP server")
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to MCP server: {e}")
            raise
    
    async def _refresh_tools(self):
        """Refresh the list of available tools from the MCP server."""
        try:
            if not self.figma_server or not self.is_connected:
                return
            
            # Import required classes
            from agents.run_context import RunContextWrapper
            from agents import Agent
            
            # Create temporary context and agent for tool listing
            run_context = RunContextWrapper(context=None)
            temp_agent = Agent(name="mcp_client", instructions="MCP tool client")
            
            # Get tools from the server
            tools = await self.figma_server.list_tools(run_context, temp_agent)
            self.available_tools = [tool.dict() if hasattr(tool, 'dict') else tool for tool in tools]
            
            logger.info(f"📋 Refreshed {len(self.available_tools)} MCP tools")
            
        except Exception as e:
            logger.error(f"❌ Failed to refresh MCP tools: {e}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on the MCP server."""
        try:
            if not self.figma_server or not self.is_connected:
                raise RuntimeError("MCP server not connected")
            
            # Import required classes
            from agents.run_context import RunContextWrapper
            from agents import Agent
            
            # Create context for the tool call
            run_context = RunContextWrapper(context=None)
            temp_agent = Agent(name="mcp_client", instructions="MCP tool client")
            
            # Call the tool
            result = await self.figma_server.call_tool(
                run_context=run_context,
                agent=temp_agent,
                tool_name=tool_name,
                arguments=arguments
            )
            
            logger.info(f"🔧 Called MCP tool: {tool_name}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to call MCP tool {tool_name}: {e}")
            raise
    
    async def get_figma_tools(self) -> List[Dict[str, Any]]:
        """Get all available Figma tools from the MCP server."""
        return self.available_tools
    
    async def send_to_figma(self, channel_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Send data to Figma through the MCP server."""
        try:
            # Use the active plugin channel ID from settings
            active_channel = getattr(settings, 'figma_plugin_channel_id', 'oa34ym6m')
            
            result = await self.call_tool("send_to_figma", {
                "channel_id": active_channel,
                "data": data
            })
            
            logger.info(f"📤 Sent data to Figma channel: {active_channel}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to send data to Figma: {e}")
            raise
    
    async def receive_from_figma(self, channel_id: str) -> Dict[str, Any]:
        """Receive data from Figma through the MCP server."""
        try:
            # Use the active plugin channel ID from settings
            active_channel = getattr(settings, 'figma_plugin_channel_id', 'pmdbxbo8')
            
            result = await self.call_tool("receive_from_figma", {
                "channel_id": active_channel
            })
            
            logger.info(f"📥 Received data from Figma channel: {active_channel}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to receive data from Figma: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the MCP service and clean up resources."""
        try:
            logger.info("🛑 Shutting down MCP Service...")
            
            # Close MCP server connection
            if self.figma_server and self.is_connected:
                await self.figma_server.__aexit__(None, None, None)
                self.is_connected = False
            
            # Terminate server process
            if self.server_process and self.server_process.poll() is None:
                self.server_process.terminate()
                try:
                    self.server_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.server_process.kill()
            
            logger.info("✅ MCP Service shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Error during MCP shutdown: {e}")
    
    @asynccontextmanager
    async def get_server(self):
        """Context manager for getting the MCP server instance."""
        if not self.figma_server or not self.is_connected:
            raise RuntimeError("MCP server not connected")
        
        try:
            yield self.figma_server
        finally:
            pass  # Server cleanup handled by shutdown()


# Global MCP service instance
mcp_service = MCPService()
