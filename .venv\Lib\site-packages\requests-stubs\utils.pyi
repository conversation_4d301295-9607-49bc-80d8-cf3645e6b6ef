import sys
from _typeshed import Incomplete, StrOrBytesPath
from collections.abc import Generator, Iterable, Mapping
from contextlib import _GeneratorContextManager
from io import BufferedWriter
from typing import AnyStr
from typing_extensions import TypeAlias

from . import compat, cookies, exceptions, structures
from .models import PreparedRequest, Request

_Uri: TypeAlias = str | bytes
OrderedDict = compat.OrderedDict
cookiejar_from_dict = cookies.cookiejar_from_dict
CaseInsensitiveDict = structures.CaseInsensitiveDict
InvalidURL = exceptions.InvalidURL

NETRC_FILES: tuple[str, str]
DEFAULT_CA_BUNDLE_PATH: Incomplete
DEFAULT_PORTS: dict[str, int]
DEFAULT_ACCEPT_ENCODING: str

def dict_to_sequence(d): ...
def super_len(o): ...
def get_netrc_auth(url: _Uri, raise_errors: bool = False) -> tuple[str, str] | None: ...
def guess_filename(obj): ...
def extract_zipped_paths(path): ...
def atomic_open(filename: StrOrBytesPath) -> _GeneratorContextManager[BufferedWriter]: ...
def from_key_val_list(value): ...
def to_key_val_list(value): ...
def parse_list_header(value): ...
def parse_dict_header(value): ...
def unquote_header_value(value, is_filename: bool = False): ...
def dict_from_cookiejar(cj): ...
def add_dict_to_cookiejar(cj, cookie_dict): ...
def get_encodings_from_content(content): ...
def get_encoding_from_headers(headers: Mapping[str, str]) -> str | None: ...
def stream_decode_response_unicode(iterator, r): ...
def iter_slices(string: str, slice_length: int | None) -> Generator[str, None, None]: ...
def get_unicode_from_response(r): ...

UNRESERVED_SET: frozenset[str]

def unquote_unreserved(uri: str) -> str: ...
def requote_uri(uri: str) -> str: ...
def address_in_network(ip: str, net: str) -> bool: ...
def dotted_netmask(mask: int) -> str: ...
def is_ipv4_address(string_ip: str) -> bool: ...
def is_valid_cidr(string_network: str) -> bool: ...
def set_environ(env_name: str, value: None) -> _GeneratorContextManager[None]: ...
def should_bypass_proxies(url: _Uri, no_proxy: Iterable[str] | None) -> bool: ...
def get_environ_proxies(url: _Uri, no_proxy: Iterable[str] | None = None) -> dict[Incomplete, Incomplete]: ...
def select_proxy(url: _Uri, proxies: Mapping[str, str] | None) -> str: ...
def resolve_proxies(
    request: Request | PreparedRequest, proxies: dict[str, str] | None, trust_env: bool = True
) -> dict[str, str]: ...
def default_user_agent(name: str = "python-requests") -> str: ...
def default_headers() -> CaseInsensitiveDict[str]: ...
def parse_header_links(value: str) -> list[dict[str, str]]: ...
def guess_json_utf(data): ...
def prepend_scheme_if_needed(url, new_scheme): ...
def get_auth_from_url(url: _Uri) -> tuple[str, str]: ...
def to_native_string(string, encoding="ascii"): ...
def urldefragauth(url: _Uri): ...
def rewind_body(prepared_request: PreparedRequest) -> None: ...
def check_header_validity(header: tuple[AnyStr, AnyStr]) -> None: ...

if sys.platform == "win32":
    def proxy_bypass_registry(host: str) -> bool: ...
    def proxy_bypass(host: str) -> bool: ...
