"""FastAPI application for the Figma Agent System."""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, List, Optional
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from config.settings import settings
from models.schemas import (
    DesignRequest, DesignResponse, AgentType, SystemStatus, AgentProgress
)
from router.design_router import design_router
from agents.agent_registry import agent_registry


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    print("🚀 Starting Figma Agent System...")
    print(f"📊 Loaded {len(agent_registry.get_available_agent_types())} agents")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Figma Agent System...")


# Create FastAPI application
app = FastAPI(
    title="Figma Agent System API",
    description="Multi-Agent Design System with OpenAI SDK Architecture",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Figma Agent System API",
        "version": "1.0.0",
        "status": "running",
        "agents_available": len(agent_registry.get_available_agent_types())
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "agents_loaded": len(agent_registry.get_available_agent_types())
    }


@app.post("/design/request", response_model=DesignResponse)
async def create_design_request(
    request: DesignRequest,
    background_tasks: BackgroundTasks
) -> DesignResponse:
    """Create a new design request."""
    try:
        # Process the design request
        response = await design_router.process_design_request(request)
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process design request: {str(e)}"
        )


@app.get("/design/request/{request_id}", response_model=DesignResponse)
async def get_design_request(request_id: str) -> DesignResponse:
    """Get status of a design request."""
    response = design_router.get_request_status(request_id)
    
    if not response:
        raise HTTPException(
            status_code=404,
            detail=f"Design request {request_id} not found"
        )
    
    return response


@app.get("/design/requests", response_model=Dict[str, DesignResponse])
async def get_all_design_requests() -> Dict[str, DesignResponse]:
    """Get all active design requests."""
    return design_router.get_active_requests()


@app.delete("/design/request/{request_id}")
async def cancel_design_request(request_id: str):
    """Cancel a design request."""
    success = await design_router.cancel_request(request_id)
    
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"Design request {request_id} not found"
        )
    
    return {"message": f"Design request {request_id} cancelled"}


@app.get("/agents/types", response_model=List[str])
async def get_agent_types() -> List[str]:
    """Get all available agent types."""
    return [agent_type.value for agent_type in agent_registry.get_available_agent_types()]


@app.get("/agents/capabilities")
async def get_agent_capabilities():
    """Get capabilities for all agents."""
    capabilities = agent_registry.get_agent_capabilities()
    return {
        agent_type.value: caps 
        for agent_type, caps in capabilities.items()
    }


@app.get("/agents/tools")
async def get_agent_tools():
    """Get tools for all agents."""
    tools = agent_registry.get_agent_tools()
    return {
        agent_type.value: tool_names 
        for agent_type, tool_names in tools.items()
    }


@app.get("/agents/status")
async def get_agents_status():
    """Get status of all agents."""
    return agent_registry.get_system_status()


@app.get("/agents/{agent_type}/status")
async def get_agent_status(agent_type: str):
    """Get status of a specific agent."""
    try:
        agent_type_enum = AgentType(agent_type)
        agent = agent_registry.get_agent(agent_type_enum)
        return agent.get_status()
        
    except ValueError:
        raise HTTPException(
            status_code=404,
            detail=f"Agent type {agent_type} not found"
        )


@app.get("/agents/{agent_type}/tools")
async def get_agent_tools_detail(agent_type: str):
    """Get detailed tool information for a specific agent."""
    try:
        agent_type_enum = AgentType(agent_type)
        agent = agent_registry.get_agent(agent_type_enum)
        tools = agent.get_tool_definitions()
        
        return {
            "agent_type": agent_type,
            "tools": [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": [
                        {
                            "name": param.name,
                            "type": param.type,
                            "description": param.description,
                            "required": param.required,
                            "enum": param.enum
                        }
                        for param in tool.parameters
                    ]
                }
                for tool in tools
            ]
        }
        
    except ValueError:
        raise HTTPException(
            status_code=404,
            detail=f"Agent type {agent_type} not found"
        )


@app.get("/system/status", response_model=SystemStatus)
async def get_system_status() -> SystemStatus:
    """Get overall system status."""
    agent_status = agent_registry.get_system_status()
    active_requests = design_router.get_active_requests()
    
    # Count completed and failed tasks
    completed_tasks = sum(
        1 for response in active_requests.values() 
        if response.status == "completed"
    )
    failed_tasks = sum(
        1 for response in active_requests.values() 
        if response.status == "failed"
    )
    
    return SystemStatus(
        active_agents=agent_status["active_agents"],
        completed_tasks=completed_tasks,
        failed_tasks=failed_tasks,
        system_health="healthy",
        uptime=0.0  # TODO: Implement actual uptime tracking
    )


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc),
            "type": type(exc).__name__
        }
    )


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    return app


async def start_server():
    """Start the FastAPI server."""
    config = uvicorn.Config(
        app=app,
        host=settings.fastapi_host,
        port=settings.fastapi_port,
        log_level=settings.log_level.lower(),
        reload=False
    )
    
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(start_server())
