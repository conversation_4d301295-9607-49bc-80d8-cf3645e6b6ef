Metadata-Version: 2.4
Name: figma-agent-system
Version: 1.0.0
Summary: Streamlit-FastAPI Multi-Agent Figma Design System with OpenAI SDK Architecture
Author-email: Figma Agent System <<EMAIL>>
Requires-Python: >=3.11
Requires-Dist: aiofiles>=23.2.1
Requires-Dist: asyncio-mqtt>=0.13.0
Requires-Dist: fastapi>=0.104.1
Requires-Dist: google-generativeai>=0.3.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: jinja2>=3.1.2
Requires-Dist: loguru>=0.7.2
Requires-Dist: openai-agents>=0.1.0
Requires-Dist: openai>=1.3.0
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: rich>=13.7.0
Requires-Dist: streamlit>=1.28.0
Requires-Dist: typing-extensions>=4.8.0
Requires-Dist: uvicorn[standard]>=0.24.0
Requires-Dist: websockets>=12.0
Provides-Extra: dev
Requires-Dist: black>=23.0.0; extra == 'dev'
Requires-Dist: isort>=5.12.0; extra == 'dev'
Requires-Dist: mypy>=1.7.0; extra == 'dev'
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'dev'
Requires-Dist: pytest>=7.4.0; extra == 'dev'
Description-Content-Type: text/markdown

# 🎨 Figma Agent System

A production-ready **Streamlit-FastAPI Multi-Agent Design System** with OpenAI SDK Architecture, featuring 31 specialized AI agents for comprehensive design automation.

## 🚀 Features

- **31 Specialized Agents**: Covering all aspects of design system creation
- **OpenAI SDK Architecture**: Following industry-standard patterns for tool integration
- **Google Gemini LLM**: Intelligent prompt analysis and agent orchestration
- **Async/Await Throughout**: Optimal performance with concurrent agent execution
- **Streamlit Frontend**: Intuitive interface with real-time progress tracking
- **FastAPI Backend**: RESTful API with comprehensive endpoints
- **Production Ready**: Proper error handling, logging, and monitoring

## 🏗️ Architecture

### Agent Categories

#### 🎯 Core Design Agents (8)
- Layout Grid Designer
- Component Creator  
- Design Token Manager
- Typography Specialist ✅
- Color Palette Generator ✅
- Icon & Asset Manager
- Spacing & Layout
- Border & Shadow

#### 🚀 Advanced Design Agents (8)
- Accessibility Specialist
- Responsive Design
- Animation & Micro-interactions
- Prototyping Assistant
- Design System Governance
- Brand Consistency
- UX Pattern Library
- Visual Hierarchy

#### ⚙️ Technical Integration Agents (8)
- Figma Export Manager
- Code Generation Bridge
- Design-Dev Bridge
- Version Control
- Quality Assurance
- Performance Optimizer
- Cross-Platform Adapter
- Localization Manager

#### 🔄 Workflow & Collaboration Agents (7)
- Project Coordinator
- Feedback Collector
- Documentation Generator
- Stakeholder Communication
- Design Trend Analyzer
- Error Handler
- Communication Channel

## 📋 Prerequisites

- **Python 3.11+**
- **uv package manager** ([Installation Guide](https://docs.astral.sh/uv/getting-started/installation/))
- **Google Gemini API Key**

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Figma-Agent
   ```

2. **Install dependencies with uv**
   ```bash
   uv sync
   ```

3. **Configure environment**
   ```bash
   cp .env.template .env
   # Edit .env and add your GEMINI_API_KEY
   ```

4. **Run the system**
   ```bash
   python run.py
   ```

## 🎮 Usage

### Starting the System

The system runs both FastAPI backend and Streamlit frontend:

- **FastAPI API**: http://localhost:8000
- **Streamlit UI**: http://localhost:8501

### Creating Design Requests

1. Open the Streamlit interface
2. Fill in the design prompt with detailed requirements
3. Select project settings (type, priority, devices)
4. Submit the request
5. Monitor real-time progress as agents work
6. Review consolidated results and implementation plans

### API Endpoints

- `POST /design/request` - Create new design request
- `GET /design/request/{id}` - Get request status
- `GET /agents/types` - List available agents
- `GET /agents/capabilities` - Get agent capabilities
- `GET /system/status` - System health check

## 🔧 Configuration

### Environment Variables

```env
# Required
GEMINI_API_KEY=your_gemini_api_key_here

# Optional
FASTAPI_HOST=localhost
FASTAPI_PORT=8000
STREAMLIT_PORT=8501
LOG_LEVEL=INFO
MAX_CONCURRENT_AGENTS=5
AGENT_TIMEOUT=300
```

### Agent Configuration

Agents can be configured through the settings:
- Timeout settings
- Concurrency limits
- Tool parameters
- Output formats

## 🏃‍♂️ Running Individual Components

### FastAPI Only
```bash
python main.py fastapi
```

### Streamlit Only
```bash
python main.py streamlit
```

### Development Mode
```bash
# Terminal 1: FastAPI with auto-reload
uvicorn api.fastapi_app:app --reload --port 8000

# Terminal 2: Streamlit with auto-reload
streamlit run frontend/streamlit_app.py --server.port 8501
```

## 📁 Project Structure

```
Figma-Agent/
├── agents/                 # Agent implementations
│   ├── base_agent.py      # Base agent class
│   ├── agent_registry.py  # Agent management
│   └── core_design/       # Core design agents
├── api/                   # FastAPI backend
│   └── fastapi_app.py     # Main API application
├── config/                # Configuration
│   └── settings.py        # Settings management
├── frontend/              # Streamlit frontend
│   └── streamlit_app.py   # Main UI application
├── models/                # Data models
│   └── schemas.py         # Pydantic schemas
├── router/                # Request routing
│   └── design_router.py   # Main router logic
├── services/              # External services
│   └── gemini_service.py  # Google Gemini integration
├── main.py               # Application entry point
├── run.py                # Simple startup script
└── pyproject.toml        # Project configuration
```

## 🧪 Development Status

### Phase 1: Core Application ✅
- [x] Project structure and configuration
- [x] Base agent architecture with OpenAI SDK patterns
- [x] Google Gemini LLM integration
- [x] FastAPI backend with comprehensive endpoints
- [x] Streamlit frontend with real-time UI
- [x] Router system with intelligent orchestration
- [x] 4 core agents implemented (Layout, Color, Typography, Component)
- [x] Agent registry with placeholder system

### Phase 2: MCP Integration (Planned)
- [ ] Figma MCP server integration
- [ ] WebSocket communication channels
- [ ] Real-time Figma synchronization
- [ ] Plugin communication protocols

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the existing issues
2. Create a new issue with detailed description
3. Include system information and error logs

---

**Built with ❤️ using OpenAI SDK Architecture, Google Gemini, FastAPI, and Streamlit**
