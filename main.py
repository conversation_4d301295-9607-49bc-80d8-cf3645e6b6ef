"""Main entry point for the Figma Agent System."""

import asyncio
import subprocess
import sys
import time
from pathlib import Path
from typing import Optional
import uvicorn

from config.settings import settings


class FigmaAgentSystem:
    """Main application class for the Figma Agent System."""
    
    def __init__(self):
        """Initialize the system."""
        self.fastapi_process: Optional[subprocess.Popen] = None
        self.streamlit_process: Optional[subprocess.Popen] = None
    
    async def start_fastapi_server(self):
        """Start the FastAPI server."""
        print("🚀 Starting FastAPI server...")
        
        try:
            from api.fastapi_app import app
            
            config = uvicorn.Config(
                app=app,
                host=settings.fastapi_host,
                port=settings.fastapi_port,
                log_level=settings.log_level.lower(),
                reload=False
            )
            
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            print(f"❌ Failed to start FastAPI server: {e}")
            raise
    
    def start_streamlit_app(self):
        """Start the Streamlit application."""
        print("🎨 Starting Streamlit frontend...")
        
        try:
            streamlit_cmd = [
                sys.executable, "-m", "streamlit", "run",
                "frontend/streamlit_app.py",
                "--server.port", str(settings.streamlit_port),
                "--server.address", settings.fastapi_host,
                "--server.headless", "true",
                "--browser.gatherUsageStats", "false"
            ]
            
            self.streamlit_process = subprocess.Popen(
                streamlit_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            print(f"✅ Streamlit started on http://{settings.fastapi_host}:{settings.streamlit_port}")
            
        except Exception as e:
            print(f"❌ Failed to start Streamlit: {e}")
            raise
    
    async def start_system(self):
        """Start the complete system."""
        print("🎯 Starting Figma Agent System...")
        print(f"📊 FastAPI: http://{settings.fastapi_host}:{settings.fastapi_port}")
        print(f"🎨 Streamlit: http://{settings.fastapi_host}:{settings.streamlit_port}")
        print("-" * 50)
        
        try:
            # Start Streamlit in background
            self.start_streamlit_app()
            
            # Give Streamlit time to start
            await asyncio.sleep(2)
            
            # Start FastAPI server (this will block)
            await self.start_fastapi_server()
            
        except KeyboardInterrupt:
            print("\n🛑 Shutting down system...")
            await self.shutdown()
        except Exception as e:
            print(f"❌ System error: {e}")
            await self.shutdown()
            raise
    
    async def shutdown(self):
        """Shutdown the system."""
        print("🔄 Shutting down services...")
        
        if self.streamlit_process:
            try:
                self.streamlit_process.terminate()
                self.streamlit_process.wait(timeout=5)
                print("✅ Streamlit stopped")
            except subprocess.TimeoutExpired:
                self.streamlit_process.kill()
                print("⚠️ Streamlit force killed")
            except Exception as e:
                print(f"❌ Error stopping Streamlit: {e}")
        
        print("✅ System shutdown complete")


async def main():
    """Main function."""
    # Validate environment
    try:
        # Check if Gemini API key is set
        if not settings.gemini_api_key or settings.gemini_api_key == "your_gemini_api_key_here":
            print("❌ GEMINI_API_KEY not configured in .env file")
            print("Please set your Google Gemini API key in the .env file")
            return
        
        print("✅ Environment validation passed")
        
    except Exception as e:
        print(f"❌ Environment validation failed: {e}")
        return
    
    # Start the system
    system = FigmaAgentSystem()
    await system.start_system()


def run_fastapi_only():
    """Run only the FastAPI server."""
    print("🚀 Starting FastAPI server only...")
    
    try:
        from api.fastapi_app import app
        
        uvicorn.run(
            app,
            host=settings.fastapi_host,
            port=settings.fastapi_port,
            log_level=settings.log_level.lower(),
            reload=False
        )
        
    except Exception as e:
        print(f"❌ Failed to start FastAPI server: {e}")


def run_streamlit_only():
    """Run only the Streamlit application."""
    print("🎨 Starting Streamlit frontend only...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "frontend/streamlit_app.py",
            "--server.port", str(settings.streamlit_port),
            "--server.address", settings.fastapi_host
        ])
        
    except Exception as e:
        print(f"❌ Failed to start Streamlit: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "fastapi":
            run_fastapi_only()
        elif sys.argv[1] == "streamlit":
            run_streamlit_only()
        else:
            print("Usage: python main.py [fastapi|streamlit]")
    else:
        # Run the complete system
        asyncio.run(main())
