"""Pydantic models and schemas for the Figma Agent System."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field


class AgentType(str, Enum):
    """Types of agents in the system."""
    # Core Design Agents
    LAYOUT_GRID = "layout_grid_designer"
    COMPONENT_CREATOR = "component_creator"
    DESIGN_TOKEN = "design_token_manager"
    TYPOGRAPHY = "typography_specialist"
    COLOR_PALETTE = "color_palette_generator"
    ICON_ASSET = "icon_asset_manager"
    SPACING_ALIGNMENT = "spacing_alignment_controller"
    BORDER_SHADOW = "border_shadow_stylist"
    
    # Advanced Design Agents
    ACCESSIBILITY = "accessibility_auditor"
    RESPONSIVE = "responsive_design_optimizer"
    ANIMATION = "animation_transition_designer"
    PROTOTYPING = "prototyping_specialist"
    DESIGN_GOVERNANCE = "design_system_governance"
    BRAND_GUIDELINES = "brand_guidelines_enforcer"
    UX_VALIDATOR = "user_experience_validator"
    VISUAL_HIERARCHY = "visual_hierarchy_optimizer"
    
    # Technical Integration Agents
    EXPORT_OPTIMIZER = "export_asset_optimizer"
    CODE_GENERATION = "code_generation_assistant"
    DESIGN_DEV_BRIDGE = "design_development_bridge"
    VERSION_CONTROL = "version_control_manager"
    QUALITY_ASSURANCE = "quality_assurance_tester"
    PERFORMANCE = "performance_analyzer"
    CROSS_PLATFORM = "cross_platform_adapter"
    LOCALIZATION = "localization_specialist"
    
    # Workflow & Collaboration Agents
    PROJECT_MANAGER = "project_manager"
    FEEDBACK_COLLECTOR = "feedback_collector"
    DOCUMENTATION = "documentation_generator"
    STAKEHOLDER_COMM = "stakeholder_communicator"
    TREND_ANALYZER = "design_trend_analyzer"
    ERROR_HANDLER = "error_handler_recovery"
    CHANNEL_CONNECTOR = "channel_connector"


class AgentStatus(str, Enum):
    """Status of an agent."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


class TaskPriority(str, Enum):
    """Priority levels for tasks."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class DesignRequest(BaseModel):
    """Request for design task."""
    prompt: str = Field(..., description="Design prompt or requirement")
    project_name: Optional[str] = Field(None, description="Project name")
    design_type: Optional[str] = Field(None, description="Type of design")
    requirements: Optional[Dict[str, Any]] = Field(
        default_factory=dict, 
        description="Additional requirements"
    )
    priority: TaskPriority = Field(
        default=TaskPriority.MEDIUM, 
        description="Task priority"
    )


class AgentTask(BaseModel):
    """Task assigned to an agent."""
    task_id: str = Field(..., description="Unique task identifier")
    agent_type: AgentType = Field(..., description="Type of agent")
    prompt: str = Field(..., description="Task prompt")
    parameters: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Task parameters"
    )
    priority: TaskPriority = Field(
        default=TaskPriority.MEDIUM, 
        description="Task priority"
    )
    created_at: datetime = Field(
        default_factory=datetime.now, 
        description="Task creation time"
    )


class AgentResponse(BaseModel):
    """Response from an agent."""
    agent_type: AgentType = Field(..., description="Type of agent")
    task_id: str = Field(..., description="Task identifier")
    status: AgentStatus = Field(..., description="Agent status")
    result: Optional[Dict[str, Any]] = Field(
        None, 
        description="Agent result data"
    )
    error: Optional[str] = Field(None, description="Error message if failed")
    execution_time: Optional[float] = Field(
        None, 
        description="Execution time in seconds"
    )
    timestamp: datetime = Field(
        default_factory=datetime.now, 
        description="Response timestamp"
    )


class AgentProgress(BaseModel):
    """Progress information for an agent."""
    agent_type: AgentType = Field(..., description="Type of agent")
    task_id: str = Field(..., description="Task identifier")
    status: AgentStatus = Field(..., description="Current status")
    progress_percentage: float = Field(
        default=0.0, 
        description="Progress percentage (0-100)"
    )
    current_step: Optional[str] = Field(
        None, 
        description="Current step description"
    )
    estimated_completion: Optional[datetime] = Field(
        None, 
        description="Estimated completion time"
    )


class SystemStatus(BaseModel):
    """Overall system status."""
    active_agents: int = Field(..., description="Number of active agents")
    completed_tasks: int = Field(..., description="Number of completed tasks")
    failed_tasks: int = Field(..., description="Number of failed tasks")
    system_health: str = Field(..., description="System health status")
    uptime: float = Field(..., description="System uptime in seconds")


class DesignResponse(BaseModel):
    """Complete response for a design request."""
    request_id: str = Field(..., description="Request identifier")
    status: str = Field(..., description="Overall status")
    agent_responses: List[AgentResponse] = Field(
        default_factory=list, 
        description="Individual agent responses"
    )
    consolidated_result: Optional[Dict[str, Any]] = Field(
        None, 
        description="Consolidated design result"
    )
    execution_summary: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Execution summary"
    )
    created_at: datetime = Field(
        default_factory=datetime.now, 
        description="Response creation time"
    )
