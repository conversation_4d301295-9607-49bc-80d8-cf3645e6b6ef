"""Component Creator Agent."""

from typing import Any, Dict, List
from agents.base_agent import BaseAgent, ToolDefinition, ToolParameter
from models.schemas import AgentType, AgentTask


class ComponentCreatorAgent(BaseAgent):
    """Agent specialized in building reusable UI components."""
    
    def __init__(self):
        """Initialize the Component Creator Agent."""
        super().__init__(AgentType.COMPONENT_CREATOR)
    
    @property
    def name(self) -> str:
        """Get the agent name."""
        return "Component Creator"
    
    @property
    def description(self) -> str:
        """Get the agent description."""
        return "Builds reusable UI components and component libraries for design systems"
    
    @property
    def capabilities(self) -> List[str]:
        """Get the agent capabilities."""
        return [
            "UI component design and specification",
            "Component state management",
            "Variant and prop definition",
            "Component composition patterns",
            "Accessibility integration",
            "Responsive component behavior",
            "Component documentation",
            "Design token integration"
        ]
    
    def _setup_tools(self) -> None:
        """Setup agent-specific tools."""
        # Component Definition Tool
        component_tool = self.create_tool_definition(
            name="define_component",
            description="Define a reusable UI component with variants and states",
            parameters=[
                self.create_tool_parameter(
                    "component_type", "string", "Type of component", True,
                    enum=["button", "input", "card", "modal", "navigation", "form", "data-display"]
                ),
                self.create_tool_parameter(
                    "variants", "array", "Component variants", True
                ),
                self.create_tool_parameter(
                    "states", "array", "Component states", True
                )
            ]
        )
        
        # Component Library Tool
        library_tool = self.create_tool_definition(
            name="create_component_library",
            description="Create a comprehensive component library structure",
            parameters=[
                self.create_tool_parameter(
                    "library_scope", "string", "Scope of the component library", True
                ),
                self.create_tool_parameter(
                    "component_categories", "array", "Categories of components", True
                )
            ]
        )
        
        # Component Composition Tool
        composition_tool = self.create_tool_definition(
            name="define_component_composition",
            description="Define how components compose together",
            parameters=[
                self.create_tool_parameter(
                    "parent_component", "string", "Parent component name", True
                ),
                self.create_tool_parameter(
                    "child_components", "array", "Child components", True
                ),
                self.create_tool_parameter(
                    "composition_rules", "object", "Rules for composition", True
                )
            ]
        )
        
        self.add_tool(component_tool)
        self.add_tool(library_tool)
        self.add_tool(composition_tool)
    
    async def _execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """Execute the component creation task."""
        prompt = task.prompt
        parameters = task.parameters
        
        # Analyze component requirements
        component_analysis = await self._analyze_component_requirements(prompt, parameters)
        
        # Create component specifications
        component_specs = await self._create_component_specifications(component_analysis)
        
        # Define component library structure
        library_structure = await self._define_library_structure(component_analysis)
        
        # Generate component documentation
        documentation = await self._generate_component_documentation(component_specs)
        
        return {
            "component_analysis": component_analysis,
            "component_specifications": component_specs,
            "library_structure": library_structure,
            "documentation": documentation,
            "implementation_examples": await self._generate_implementation_examples(component_specs),
            "testing_guidelines": await self._generate_testing_guidelines()
        }
    
    async def _analyze_component_requirements(
        self, prompt: str, parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze component requirements from the prompt."""
        return {
            "component_types": parameters.get("component_types", ["button", "input", "card"]),
            "design_system_scope": parameters.get("design_system_scope", "web"),
            "accessibility_requirements": parameters.get("accessibility_requirements", True),
            "responsive_behavior": parameters.get("responsive_behavior", True),
            "theming_support": parameters.get("theming_support", True),
            "framework_target": parameters.get("framework_target", "framework-agnostic")
        }
    
    async def _create_component_specifications(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create detailed component specifications."""
        components = {}
        
        # Button Component
        components["button"] = {
            "name": "Button",
            "description": "Interactive button component with multiple variants",
            "props": {
                "variant": {
                    "type": "string",
                    "options": ["primary", "secondary", "outline", "ghost", "danger"],
                    "default": "primary"
                },
                "size": {
                    "type": "string", 
                    "options": ["sm", "md", "lg"],
                    "default": "md"
                },
                "disabled": {
                    "type": "boolean",
                    "default": False
                },
                "loading": {
                    "type": "boolean",
                    "default": False
                },
                "icon": {
                    "type": "string",
                    "optional": True
                },
                "fullWidth": {
                    "type": "boolean",
                    "default": False
                }
            },
            "states": ["default", "hover", "active", "focus", "disabled", "loading"],
            "accessibility": {
                "role": "button",
                "keyboard_support": ["Enter", "Space"],
                "aria_attributes": ["aria-label", "aria-disabled", "aria-pressed"]
            },
            "styling": {
                "base_styles": {
                    "display": "inline-flex",
                    "align_items": "center",
                    "justify_content": "center",
                    "border_radius": "var(--radius-md)",
                    "font_weight": "500",
                    "transition": "all 0.2s ease",
                    "cursor": "pointer"
                },
                "size_variants": {
                    "sm": {"padding": "8px 12px", "font_size": "14px", "height": "32px"},
                    "md": {"padding": "12px 16px", "font_size": "16px", "height": "40px"},
                    "lg": {"padding": "16px 24px", "font_size": "18px", "height": "48px"}
                },
                "color_variants": {
                    "primary": {
                        "background": "var(--color-primary-500)",
                        "color": "white",
                        "border": "1px solid var(--color-primary-500)"
                    },
                    "secondary": {
                        "background": "var(--color-neutral-100)",
                        "color": "var(--color-neutral-900)",
                        "border": "1px solid var(--color-neutral-300)"
                    }
                }
            }
        }
        
        # Input Component
        components["input"] = {
            "name": "Input",
            "description": "Text input component with validation states",
            "props": {
                "type": {
                    "type": "string",
                    "options": ["text", "email", "password", "number", "tel", "url"],
                    "default": "text"
                },
                "size": {
                    "type": "string",
                    "options": ["sm", "md", "lg"],
                    "default": "md"
                },
                "variant": {
                    "type": "string",
                    "options": ["default", "filled", "outline"],
                    "default": "outline"
                },
                "state": {
                    "type": "string",
                    "options": ["default", "error", "success", "warning"],
                    "default": "default"
                },
                "placeholder": {
                    "type": "string",
                    "optional": True
                },
                "disabled": {
                    "type": "boolean",
                    "default": False
                },
                "required": {
                    "type": "boolean",
                    "default": False
                }
            },
            "states": ["default", "focus", "hover", "disabled", "error", "success"],
            "accessibility": {
                "role": "textbox",
                "aria_attributes": ["aria-label", "aria-describedby", "aria-invalid", "aria-required"]
            }
        }
        
        # Card Component
        components["card"] = {
            "name": "Card",
            "description": "Container component for grouping related content",
            "props": {
                "variant": {
                    "type": "string",
                    "options": ["default", "outlined", "elevated", "filled"],
                    "default": "default"
                },
                "padding": {
                    "type": "string",
                    "options": ["none", "sm", "md", "lg"],
                    "default": "md"
                },
                "interactive": {
                    "type": "boolean",
                    "default": False
                }
            },
            "composition": {
                "slots": ["header", "content", "footer"],
                "sub_components": ["CardHeader", "CardContent", "CardFooter"]
            }
        }
        
        return components
    
    async def _define_library_structure(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Define the component library structure."""
        return {
            "categories": {
                "foundations": {
                    "description": "Basic design tokens and utilities",
                    "components": ["Colors", "Typography", "Spacing", "Shadows", "Borders"]
                },
                "layout": {
                    "description": "Layout and structure components",
                    "components": ["Container", "Grid", "Stack", "Flex", "Divider"]
                },
                "forms": {
                    "description": "Form and input components",
                    "components": ["Input", "Textarea", "Select", "Checkbox", "Radio", "Switch"]
                },
                "navigation": {
                    "description": "Navigation and menu components",
                    "components": ["Button", "Link", "Breadcrumb", "Tabs", "Menu", "Pagination"]
                },
                "feedback": {
                    "description": "Feedback and status components",
                    "components": ["Alert", "Toast", "Badge", "Progress", "Spinner", "Skeleton"]
                },
                "overlay": {
                    "description": "Overlay and modal components",
                    "components": ["Modal", "Drawer", "Popover", "Tooltip", "Dropdown"]
                },
                "data_display": {
                    "description": "Data display components",
                    "components": ["Table", "List", "Card", "Avatar", "Image", "Icon"]
                }
            },
            "naming_convention": {
                "pattern": "PascalCase",
                "prefix": "",
                "examples": ["Button", "InputField", "DataTable"]
            },
            "file_structure": {
                "component_folder": "components/",
                "story_folder": "stories/",
                "test_folder": "__tests__/",
                "type_folder": "types/"
            }
        }
    
    async def _generate_component_documentation(self, component_specs: Dict[str, Any]) -> Dict[str, Any]:
        """Generate component documentation."""
        documentation = {}
        
        for component_name, spec in component_specs.items():
            documentation[component_name] = {
                "overview": {
                    "name": spec["name"],
                    "description": spec["description"],
                    "category": "forms" if component_name == "input" else "navigation" if component_name == "button" else "data_display"
                },
                "api": {
                    "props": spec["props"],
                    "states": spec.get("states", []),
                    "accessibility": spec.get("accessibility", {})
                },
                "examples": {
                    "basic": f"<{spec['name']} />",
                    "with_props": f"<{spec['name']} variant='primary' size='lg' />",
                    "disabled": f"<{spec['name']} disabled />"
                },
                "guidelines": {
                    "when_to_use": f"Use {spec['name']} when you need...",
                    "best_practices": [
                        f"Always provide meaningful labels for {spec['name']}",
                        f"Consider the context when choosing {spec['name']} variants",
                        f"Test {spec['name']} with keyboard navigation"
                    ]
                }
            }
        
        return documentation
    
    async def _generate_implementation_examples(self, component_specs: Dict[str, Any]) -> Dict[str, Any]:
        """Generate implementation examples for components."""
        examples = {}
        
        for component_name, spec in component_specs.items():
            examples[component_name] = {
                "react": {
                    "basic": f"""
import {{ {spec['name']} }} from '@/components/{spec['name']}';

export function Example() {{
  return <{spec['name']} />;
}}""",
                    "with_props": f"""
import {{ {spec['name']} }} from '@/components/{spec['name']}';

export function Example() {{
  return (
    <{spec['name']}
      variant="primary"
      size="md"
      onClick={{() => console.log('clicked')}}
    >
      Click me
    </{spec['name']}>
  );
}}"""
                },
                "html_css": {
                    "basic": f"""
<{component_name.lower()} class="{component_name.lower()}">
  {spec['name']} content
</{component_name.lower()}>""",
                    "styled": f"""
<{component_name.lower()} class="{component_name.lower()} {component_name.lower()}--primary {component_name.lower()}--md">
  {spec['name']} content
</{component_name.lower()}>"""
                }
            }
        
        return examples
    
    async def _generate_testing_guidelines(self) -> List[str]:
        """Generate component testing guidelines."""
        return [
            "Test all component variants and states",
            "Verify keyboard navigation and accessibility",
            "Test responsive behavior across breakpoints",
            "Validate prop types and default values",
            "Test component composition and nesting",
            "Verify theme integration and customization",
            "Test error states and edge cases",
            "Validate performance with large datasets"
        ]
