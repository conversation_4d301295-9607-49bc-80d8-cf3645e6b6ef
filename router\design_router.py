"""Design Router - Central orchestration hub for multi-agent design system."""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor

from agents.agent_registry import agent_registry
from models.schemas import (
    AgentType, AgentTask, AgentResponse, DesignRequest, 
    DesignResponse, TaskPriority, AgentStatus
)
from services.gemini_service import gemini_service
from config.settings import settings


class DesignRouter:
    """Central router for orchestrating multi-agent design tasks."""
    
    def __init__(self):
        """Initialize the design router."""
        self.active_requests: Dict[str, DesignResponse] = {}
        self.task_queue: List[AgentTask] = []
        self.executor = ThreadPoolExecutor(max_workers=settings.max_concurrent_agents)
    
    async def process_design_request(self, request: DesignRequest) -> DesignResponse:
        """Process a design request through the multi-agent system."""
        request_id = str(uuid.uuid4())
        
        # Create initial response object
        response = DesignResponse(
            request_id=request_id,
            status="processing",
            agent_responses=[],
            consolidated_result=None,
            execution_summary={},
            created_at=datetime.now()
        )
        
        # Store active request
        self.active_requests[request_id] = response
        
        try:
            # Step 1: Analyze prompt with Gemini
            analysis = await self._analyze_design_prompt(request)
            
            # Step 2: Create agent tasks
            agent_tasks = await self._create_agent_tasks(request, analysis)
            
            # Step 3: Execute agents concurrently
            agent_responses = await self._execute_agents_concurrently(agent_tasks)
            
            # Step 4: Consolidate results
            consolidated_result = await self._consolidate_results(request, agent_responses)
            
            # Step 5: Update response
            response.status = "completed"
            response.agent_responses = agent_responses
            response.consolidated_result = consolidated_result
            response.execution_summary = await self._create_execution_summary(
                analysis, agent_tasks, agent_responses
            )
            
        except Exception as e:
            response.status = "failed"
            response.execution_summary = {
                "error": str(e),
                "failed_at": datetime.now().isoformat()
            }
        
        return response
    
    async def _analyze_design_prompt(self, request: DesignRequest) -> Dict[str, Any]:
        """Analyze the design prompt using Gemini LLM."""
        try:
            analysis = await gemini_service.analyze_prompt(request.prompt)
            
            # Validate required agents exist
            valid_agents = []
            for agent_type_str in analysis["required_agents"]:
                try:
                    agent_type = AgentType(agent_type_str)
                    if agent_registry.validate_agent_type(agent_type):
                        valid_agents.append(agent_type_str)
                except ValueError:
                    # Skip invalid agent types
                    continue
            
            analysis["required_agents"] = valid_agents
            return analysis
            
        except Exception as e:
            # Fallback to basic analysis if Gemini fails
            return {
                "required_agents": ["layout_grid_designer", "color_palette_generator"],
                "task_breakdown": {
                    "layout_grid_designer": "Create responsive layout system",
                    "color_palette_generator": "Generate color palette"
                },
                "priority_order": ["layout_grid_designer", "color_palette_generator"],
                "estimated_complexity": "medium",
                "design_category": "general",
                "fallback_reason": str(e)
            }
    
    async def _create_agent_tasks(
        self, 
        request: DesignRequest, 
        analysis: Dict[str, Any]
    ) -> List[AgentTask]:
        """Create specific tasks for each required agent."""
        tasks = []
        
        for i, agent_type_str in enumerate(analysis["required_agents"]):
            try:
                agent_type = AgentType(agent_type_str)
                
                # Get specific task from analysis
                specific_task = analysis["task_breakdown"].get(
                    agent_type_str, 
                    f"Execute {agent_type_str} for design request"
                )
                
                # Generate detailed task parameters using Gemini
                try:
                    task_details = await gemini_service.generate_agent_task(
                        agent_type, request.prompt, specific_task
                    )
                except Exception:
                    # Fallback task details
                    task_details = {
                        "task_description": specific_task,
                        "parameters": {"prompt": request.prompt},
                        "success_criteria": ["Task completed successfully"],
                        "dependencies": [],
                        "output_format": "structured_json"
                    }
                
                # Create agent task
                task = AgentTask(
                    task_id=str(uuid.uuid4()),
                    agent_type=agent_type,
                    prompt=task_details["task_description"],
                    parameters=task_details["parameters"],
                    priority=request.priority,
                    created_at=datetime.now()
                )
                
                tasks.append(task)
                
            except ValueError:
                # Skip invalid agent types
                continue
        
        return tasks
    
    async def _execute_agents_concurrently(
        self, 
        tasks: List[AgentTask]
    ) -> List[AgentResponse]:
        """Execute multiple agents concurrently."""
        # Create semaphore to limit concurrent executions
        semaphore = asyncio.Semaphore(settings.max_concurrent_agents)
        
        async def execute_single_agent(task: AgentTask) -> AgentResponse:
            async with semaphore:
                try:
                    agent = agent_registry.get_agent(task.agent_type)
                    return await agent.execute(task)
                except Exception as e:
                    return AgentResponse(
                        agent_type=task.agent_type,
                        task_id=task.task_id,
                        status=AgentStatus.FAILED,
                        error=str(e),
                        timestamp=datetime.now()
                    )
        
        # Execute all tasks concurrently
        responses = await asyncio.gather(
            *[execute_single_agent(task) for task in tasks],
            return_exceptions=True
        )
        
        # Filter out exceptions and convert to AgentResponse objects
        valid_responses = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                # Create error response for exceptions
                error_response = AgentResponse(
                    agent_type=tasks[i].agent_type,
                    task_id=tasks[i].task_id,
                    status=AgentStatus.FAILED,
                    error=str(response),
                    timestamp=datetime.now()
                )
                valid_responses.append(error_response)
            else:
                valid_responses.append(response)
        
        return valid_responses
    
    async def _consolidate_results(
        self, 
        request: DesignRequest, 
        agent_responses: List[AgentResponse]
    ) -> Dict[str, Any]:
        """Consolidate results from all agents."""
        # Prepare agent results for consolidation
        agent_results = []
        for response in agent_responses:
            if response.status == AgentStatus.COMPLETED and response.result:
                agent_results.append({
                    "agent_type": response.agent_type.value,
                    "result": response.result,
                    "execution_time": response.execution_time
                })
        
        try:
            # Use Gemini to consolidate results
            consolidated = await gemini_service.consolidate_results(
                request.prompt, agent_results
            )
            return consolidated
            
        except Exception as e:
            # Fallback consolidation
            return {
                "consolidated_design": {
                    "status": "partial_consolidation",
                    "agent_results": agent_results,
                    "consolidation_error": str(e)
                },
                "implementation_plan": [
                    "Review individual agent outputs",
                    "Manually integrate design elements",
                    "Test consolidated design"
                ],
                "design_assets": [
                    result["result"] for result in agent_results
                ],
                "quality_metrics": {
                    "completed_agents": len(agent_results),
                    "total_agents": len(agent_responses)
                },
                "recommendations": [
                    "Manual review required due to consolidation error"
                ]
            }
    
    async def _create_execution_summary(
        self, 
        analysis: Dict[str, Any],
        tasks: List[AgentTask],
        responses: List[AgentResponse]
    ) -> Dict[str, Any]:
        """Create execution summary."""
        successful_responses = [r for r in responses if r.status == AgentStatus.COMPLETED]
        failed_responses = [r for r in responses if r.status == AgentStatus.FAILED]
        
        total_execution_time = sum(
            r.execution_time for r in responses if r.execution_time
        )
        
        return {
            "analysis": analysis,
            "total_agents": len(tasks),
            "successful_agents": len(successful_responses),
            "failed_agents": len(failed_responses),
            "total_execution_time": total_execution_time,
            "average_execution_time": total_execution_time / len(responses) if responses else 0,
            "agent_performance": [
                {
                    "agent_type": r.agent_type.value,
                    "status": r.status.value,
                    "execution_time": r.execution_time,
                    "error": r.error
                }
                for r in responses
            ]
        }
    
    def get_request_status(self, request_id: str) -> Optional[DesignResponse]:
        """Get status of a design request."""
        return self.active_requests.get(request_id)
    
    def get_active_requests(self) -> Dict[str, DesignResponse]:
        """Get all active requests."""
        return self.active_requests.copy()
    
    async def cancel_request(self, request_id: str) -> bool:
        """Cancel an active request."""
        if request_id in self.active_requests:
            self.active_requests[request_id].status = "cancelled"
            return True
        return False


# Global design router instance
design_router = DesignRouter()
