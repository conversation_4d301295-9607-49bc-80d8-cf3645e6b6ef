"""Base agent class following OpenAI SDK Tools pattern."""

import asyncio
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Type
from pydantic import BaseModel, Field

from models.schemas import AgentType, AgentStatus, AgentTask, AgentResponse


class ToolParameter(BaseModel):
    """Tool parameter definition following OpenAI SDK pattern."""
    name: str = Field(..., description="Parameter name")
    type: str = Field(..., description="Parameter type")
    description: str = Field(..., description="Parameter description")
    required: bool = Field(default=True, description="Whether parameter is required")
    enum: Optional[List[str]] = Field(None, description="Allowed values")


class ToolDefinition(BaseModel):
    """Tool definition following OpenAI SDK pattern."""
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    parameters: List[ToolParameter] = Field(
        default_factory=list, 
        description="Tool parameters"
    )


class BaseAgent(ABC):
    """Base agent class implementing OpenAI SDK Tools pattern."""
    
    def __init__(self, agent_type: AgentType):
        """Initialize the base agent."""
        self.agent_type = agent_type
        self.status = AgentStatus.IDLE
        self.current_task: Optional[AgentTask] = None
        self.execution_start_time: Optional[datetime] = None
        self.tools: List[ToolDefinition] = []
        self._setup_tools()
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Get the agent name."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Get the agent description."""
        pass
    
    @property
    @abstractmethod
    def capabilities(self) -> List[str]:
        """Get the agent capabilities."""
        pass
    
    @abstractmethod
    def _setup_tools(self) -> None:
        """Setup agent-specific tools."""
        pass
    
    @abstractmethod
    async def _execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """Execute the assigned task."""
        pass
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        """Execute a task and return response."""
        self.current_task = task
        self.status = AgentStatus.RUNNING
        self.execution_start_time = datetime.now()
        
        try:
            # Execute the task
            result = await self._execute_task(task)
            
            # Calculate execution time
            execution_time = (
                datetime.now() - self.execution_start_time
            ).total_seconds()
            
            # Create successful response
            response = AgentResponse(
                agent_type=self.agent_type,
                task_id=task.task_id,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=execution_time,
                timestamp=datetime.now()
            )
            
            self.status = AgentStatus.COMPLETED
            return response
            
        except Exception as e:
            # Calculate execution time even for failures
            execution_time = (
                datetime.now() - self.execution_start_time
            ).total_seconds() if self.execution_start_time else 0
            
            # Create error response
            response = AgentResponse(
                agent_type=self.agent_type,
                task_id=task.task_id,
                status=AgentStatus.FAILED,
                error=str(e),
                execution_time=execution_time,
                timestamp=datetime.now()
            )
            
            self.status = AgentStatus.FAILED
            return response
        
        finally:
            self.current_task = None
    
    def get_tool_definitions(self) -> List[ToolDefinition]:
        """Get tool definitions for this agent."""
        return self.tools
    
    def add_tool(self, tool: ToolDefinition) -> None:
        """Add a tool to this agent."""
        self.tools.append(tool)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status."""
        return {
            "agent_type": self.agent_type.value,
            "name": self.name,
            "status": self.status.value,
            "current_task_id": self.current_task.task_id if self.current_task else None,
            "execution_start_time": self.execution_start_time.isoformat() if self.execution_start_time else None,
            "capabilities": self.capabilities,
            "tools_count": len(self.tools)
        }
    
    async def validate_task(self, task: AgentTask) -> bool:
        """Validate if this agent can handle the task."""
        return task.agent_type == self.agent_type
    
    def create_tool_parameter(
        self, 
        name: str, 
        param_type: str, 
        description: str, 
        required: bool = True,
        enum: Optional[List[str]] = None
    ) -> ToolParameter:
        """Helper method to create tool parameters."""
        return ToolParameter(
            name=name,
            type=param_type,
            description=description,
            required=required,
            enum=enum
        )
    
    def create_tool_definition(
        self, 
        name: str, 
        description: str, 
        parameters: Optional[List[ToolParameter]] = None
    ) -> ToolDefinition:
        """Helper method to create tool definitions."""
        return ToolDefinition(
            name=name,
            description=description,
            parameters=parameters or []
        )
