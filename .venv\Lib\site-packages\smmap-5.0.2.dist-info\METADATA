Metadata-Version: 2.1
Name: smmap
Version: 5.0.2
Summary: A pure Python implementation of a sliding window memory map manager
Home-page: https://github.com/gitpython-developers/smmap
Author: <PERSON>
Author-email: byron<PERSON>@gmail.com
License: BSD-3-Clause
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Operating System :: POSIX
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE

## Motivation

When reading from many possibly large files in a fashion similar to random access, it is usually the fastest and most efficient to use memory maps.

Although memory maps have many advantages, they represent a very limited system resource as every map uses one file descriptor, whose amount is limited per process. On 32 bit systems, the amount of memory you can have mapped at a time is naturally limited to theoretical 4GB of memory, which may not be enough for some applications.


## Limitations

* **System resources (file-handles) are likely to be leaked!** This is due to the library authors reliance on a deterministic `__del__()` destructor.
* The memory access is read-only by design.


## Overview

![Python package](https://github.com/gitpython-developers/smmap/workflows/Python%20package/badge.svg)

Smmap wraps an interface around mmap and tracks the mapped files as well as the amount of clients who use it. If the system runs out of resources, or if a memory limit is reached, it will automatically unload unused maps to allow continued operation.

To allow processing large files even on 32 bit systems, it allows only portions of the file to be mapped. Once the user reads beyond the mapped region, smmap will automatically map the next required region, unloading unused regions using a LRU algorithm.

Although the library can be used most efficiently with its native interface, a Buffer implementation is provided to hide these details behind a simple string-like interface.

For performance critical 64 bit applications, a simplified version of memory mapping is provided which always maps the whole file, but still provides the benefit of unloading unused mappings on demand.



## Prerequisites

* Python 3.7+
* OSX, Windows or Linux

The package was tested on all of the previously mentioned configurations.

## Installing smmap

[![Documentation Status](https://readthedocs.org/projects/smmap/badge/?version=latest)](https://readthedocs.org/projects/smmap/?badge=latest)

Its easiest to install smmap using the [pip](http://www.pip-installer.org/en/latest) program:

```bash
$ pip install smmap
```

As the command will install smmap in your respective python distribution, you will most likely need root permissions to authorize the required changes.

If you have downloaded the source archive, the package can be installed by running the `setup.py` script:

```bash
$ python setup.py install
```

It is advised to have a look at the **Usage Guide** for a brief introduction on the different database implementations.



## Homepage and Links

The project is home on github at https://github.com/gitpython-developers/smmap .

The latest source can be cloned from github as well:

* git://github.com/gitpython-developers/smmap.git


For support, please use the git-python mailing list:

* http://groups.google.com/group/git-python


Issues can be filed on github:

* https://github.com/gitpython-developers/smmap/issues

A link to the pypi page related to this repository:

* https://pypi.org/project/smmap/


## License Information

*smmap* is licensed under the New BSD License.

