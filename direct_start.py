#!/usr/bin/env python3
"""Direct startup script that launches services and opens browser."""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Main function to start services."""
    print("🎨 Figma Agent System - Direct Startup")
    print("=" * 50)
    
    try:
        # Test components first
        from config.settings import settings
        from agents.agent_registry import agent_registry
        
        print(f"✅ Configuration loaded")
        print(f"   • FastAPI: http://localhost:{settings.fastapi_port}")
        print(f"   • Streamlit: http://localhost:{settings.streamlit_port}")
        print(f"✅ Agent registry loaded - {len(agent_registry.get_available_agent_types())} agents")
        
        # Start FastAPI server
        print("\n🚀 Starting FastAPI server...")
        fastapi_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "api.fastapi_app:app",
            "--host", settings.fastapi_host,
            "--port", str(settings.fastapi_port)
        ])
        
        # Wait for FastAPI to start
        time.sleep(3)
        
        if fastapi_process.poll() is None:
            print("✅ FastAPI server started successfully!")
            print(f"📊 API Documentation: http://localhost:{settings.fastapi_port}/docs")
            print(f"📊 API Health: http://localhost:{settings.fastapi_port}/system/status")
            
            # Open browser to API docs
            webbrowser.open(f"http://localhost:{settings.fastapi_port}/docs")
            
        else:
            print("❌ FastAPI server failed to start")
            return
        
        # Start Streamlit app
        print("\n🎨 Starting Streamlit app...")
        streamlit_process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run",
            "frontend/streamlit_app.py",
            "--server.port", str(settings.streamlit_port),
            "--server.address", settings.fastapi_host
        ])
        
        # Wait for Streamlit to start
        time.sleep(5)
        
        if streamlit_process.poll() is None:
            print("✅ Streamlit app started successfully!")
            print(f"🎨 Streamlit UI: http://localhost:{settings.streamlit_port}")
            
            # Open browser to Streamlit
            webbrowser.open(f"http://localhost:{settings.streamlit_port}")
            
        else:
            print("❌ Streamlit app failed to start")
        
        print("\n🎯 Both services are running!")
        print("=" * 50)
        print("📊 FastAPI Backend: http://localhost:{}".format(settings.fastapi_port))
        print("🎨 Streamlit Frontend: http://localhost:{}".format(settings.streamlit_port))
        print("📚 API Documentation: http://localhost:{}/docs".format(settings.fastapi_port))
        print("=" * 50)
        print("Press Ctrl+C to stop all services...")
        
        # Keep running until interrupted
        try:
            while True:
                time.sleep(1)
                # Check if processes are still running
                if fastapi_process.poll() is not None:
                    print("❌ FastAPI server stopped unexpectedly")
                    break
                if streamlit_process.poll() is not None:
                    print("❌ Streamlit app stopped unexpectedly")
                    break
        except KeyboardInterrupt:
            print("\n👋 Shutting down services...")
            
        # Clean up processes
        try:
            fastapi_process.terminate()
            streamlit_process.terminate()
            fastapi_process.wait(timeout=5)
            streamlit_process.wait(timeout=5)
        except:
            fastapi_process.kill()
            streamlit_process.kill()
            
        print("✅ All services stopped")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
