"""Configuration settings for the Figma Agent System."""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # API Configuration
    gemini_api_key: str = Field(..., description="Google Gemini API key")
    
    # Server Configuration
    fastapi_host: str = Field(default="localhost", description="FastAPI host")
    fastapi_port: int = Field(default=8000, description="FastAPI port")
    streamlit_port: int = Field(default=8501, description="Streamlit port")
    
    # MCP Configuration (Phase 2)
    mcp_server_path: Optional[str] = Field(
        default=None, 
        description="Path to MCP server TypeScript file"
    )
    figma_plugin_channel_id: Optional[str] = Field(
        default=None,
        description="Figma plugin channel ID"
    )
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format")
    
    # Agent Configuration
    max_concurrent_agents: int = Field(
        default=10, 
        description="Maximum concurrent agents"
    )
    agent_timeout_seconds: int = Field(
        default=300, 
        description="Agent timeout in seconds"
    )
    retry_attempts: int = Field(
        default=3, 
        description="Number of retry attempts"
    )
    
    @property
    def fastapi_url(self) -> str:
        """Get the FastAPI server URL."""
        return f"http://{self.fastapi_host}:{self.fastapi_port}"
    
    @property
    def streamlit_url(self) -> str:
        """Get the Streamlit server URL."""
        return f"http://{self.fastapi_host}:{self.streamlit_port}"


# Global settings instance
settings = Settings()
