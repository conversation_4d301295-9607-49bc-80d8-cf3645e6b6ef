# API Configuration
GEMINI_API_KEY=your_gemini_api_key_here
FIGMA_ACCESS_TOKEN=your_figma_access_token_here

# Server Configuration
FASTAPI_HOST=localhost
FASTAPI_PORT=8000
STREAMLIT_PORT=8501

# MCP Configuration (Phase 2)
MCP_SERVER_PATH=C:\Users\<USER>\Desktop\claudetalktofigma\claude-talk-to-figma-mcp\src\talk_to_figma_mcp\server.ts
FIGMA_PLUGIN_CHANNEL_ID=oa34ym6m

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Agent Configuration
MAX_CONCURRENT_AGENTS=10
AGENT_TIMEOUT_SECONDS=300
RETRY_ATTEMPTS=3
