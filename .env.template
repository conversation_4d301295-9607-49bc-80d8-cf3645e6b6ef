# API Configuration
GEMINI_API_KEY=AIzaSyCjii4ROnXRcC0hi0MywvO8N7NkJyxH_Yo

# Server Configuration
FASTAPI_HOST=localhost
FASTAPI_PORT=8000
STREAMLIT_PORT=8501

# MCP Configuration (Phase 2)
MCP_SERVER_PATH=C:\Users\<USER>\Desktop\claudetalktofigma\claude-talk-to-figma-mcp\src\talk_to_figma_mcp\server.ts

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Agent Configuration
MAX_CONCURRENT_AGENTS=10
AGENT_TIMEOUT_SECONDS=300
RETRY_ATTEMPTS=3
