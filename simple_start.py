#!/usr/bin/env python3
"""Simple startup script for testing individual components."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def start_fastapi():
    """Start FastAPI server."""
    print("🚀 Starting FastAPI server...")
    
    from config.settings import settings
    from api.fastapi_app import app
    import uvicorn
    
    print(f"📊 FastAPI will start on http://localhost:{settings.fastapi_port}")
    
    # Start the server
    config = uvicorn.Config(
        app=app,
        host=settings.fastapi_host,
        port=settings.fastapi_port,
        log_level="info"
    )
    server = uvicorn.Server(config)
    await server.serve()

async def start_streamlit():
    """Start Streamlit app."""
    print("🎨 Starting Streamlit app...")
    
    from config.settings import settings
    import subprocess
    import os
    
    # Set environment variables for Streamlit
    env = os.environ.copy()
    env['STREAMLIT_SERVER_PORT'] = str(settings.streamlit_port)
    env['STREAMLIT_SERVER_ADDRESS'] = 'localhost'
    
    print(f"🎨 Streamlit will start on http://localhost:{settings.streamlit_port}")
    
    # Start Streamlit
    process = subprocess.Popen([
        sys.executable, "-m", "streamlit", "run", 
        "frontend/streamlit_app.py",
        "--server.port", str(settings.streamlit_port),
        "--server.address", "localhost"
    ], env=env)
    
    return process

async def main():
    """Main function to start both services."""
    print("🎨 Figma Agent System - Simple Startup")
    print("=" * 50)
    
    try:
        # Test components first
        from config.settings import settings
        from agents.agent_registry import agent_registry
        
        print(f"✅ Configuration loaded - FastAPI: {settings.fastapi_port}, Streamlit: {settings.streamlit_port}")
        print(f"✅ Agent registry loaded - {len(agent_registry.get_available_agent_types())} agents")
        
        # Start FastAPI in background
        print("\n🚀 Starting FastAPI server...")
        fastapi_task = asyncio.create_task(start_fastapi())
        
        # Give FastAPI time to start
        await asyncio.sleep(2)
        
        print("✅ FastAPI server started successfully!")
        print(f"📊 API Documentation: http://localhost:{settings.fastapi_port}/docs")
        print(f"📊 API Health Check: http://localhost:{settings.fastapi_port}/system/status")
        
        # Keep the server running
        print("\n🎯 FastAPI server is running. Press Ctrl+C to stop.")
        await fastapi_task
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
