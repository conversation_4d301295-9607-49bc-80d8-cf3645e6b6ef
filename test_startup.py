#!/usr/bin/env python3
"""Test startup script to verify system components."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_system():
    """Test system components."""
    print("🧪 Testing Figma Agent System Components...")
    print("=" * 50)
    
    try:
        # Test 1: Configuration
        print("1. Testing configuration...")
        from config.settings import settings
        print(f"   ✅ Settings loaded - FastAPI port: {settings.fastapi_port}")
        
        # Test 2: Agent Registry
        print("2. Testing agent registry...")
        from agents.agent_registry import agent_registry
        available_agents = agent_registry.get_available_agent_types()
        print(f"   ✅ Agent registry loaded - {len(available_agents)} agents available")
        
        # Test 3: Gemini Service
        print("3. Testing Gemini service...")
        from services.gemini_service import gemini_service
        print("   ✅ Gemini service loaded")
        
        # Test 4: FastAPI App
        print("4. Testing FastAPI app...")
        from api.fastapi_app import app
        print("   ✅ FastAPI app loaded")
        
        # Test 5: Streamlit App
        print("5. Testing Streamlit app...")
        from frontend.streamlit_app import StreamlitApp
        print("   ✅ Streamlit app loaded")
        
        # Test 6: Design Router
        print("6. Testing design router...")
        from router.design_router import design_router
        print("   ✅ Design router loaded")
        
        print("\n🎉 All components loaded successfully!")
        print(f"📊 System ready with {len(available_agents)} agents")
        
        # List some agents
        print("\n🤖 Available agents:")
        for i, agent_type in enumerate(list(available_agents)[:5]):
            print(f"   • {agent_type.value}")
        if len(available_agents) > 5:
            print(f"   ... and {len(available_agents) - 5} more")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_system())
    if success:
        print("\n✅ System test passed - ready to start!")
    else:
        print("\n❌ System test failed")
        sys.exit(1)
