#!/usr/bin/env python3
"""Simple startup script for the Figma Agent System."""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import main


if __name__ == "__main__":
    print("🎨 Figma Agent System - Startup Script")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = project_root / ".env"
    if not env_file.exists():
        print("❌ .env file not found!")
        print("Please copy .env.template to .env and configure your API keys")
        sys.exit(1)
    
    # Check if uv is available
    try:
        import subprocess
        result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ uv package manager not found!")
            print("Please install uv: https://docs.astral.sh/uv/getting-started/installation/")
            sys.exit(1)
        print(f"✅ uv found: {result.stdout.strip()}")
    except FileNotFoundError:
        print("❌ uv package manager not found!")
        print("Please install uv: https://docs.astral.sh/uv/getting-started/installation/")
        sys.exit(1)
    
    # Run the main application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
