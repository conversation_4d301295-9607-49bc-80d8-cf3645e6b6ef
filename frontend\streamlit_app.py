"""Streamlit frontend for the Figma Agent System."""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional
import streamlit as st
import httpx
import pandas as pd

from config.settings import settings


class StreamlitApp:
    """Streamlit application for the Figma Agent System."""
    
    def __init__(self):
        """Initialize the Streamlit app."""
        self.api_base_url = settings.fastapi_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    def setup_page_config(self):
        """Setup Streamlit page configuration."""
        st.set_page_config(
            page_title="Figma Agent System",
            page_icon="🎨",
            layout="wide",
            initial_sidebar_state="expanded"
        )
    
    def render_header(self):
        """Render the application header."""
        st.title("🎨 Figma Agent System")
        st.markdown("**Multi-Agent Design System with AI-Powered Orchestration**")
        
        # System status in header
        col1, col2, col3, col4, col5 = st.columns(5)

        with col1:
            st.metric("System Status", "🟢 Online")

        with col2:
            # Get system status from API
            try:
                response = httpx.get(f"{self.api_base_url}/system/status")
                if response.status_code == 200:
                    status = response.json()
                    st.metric("Active Agents", status.get("active_agents", 0))
                else:
                    st.metric("Active Agents", "Error")
            except:
                st.metric("Active Agents", "Offline")
        
        with col3:
            st.metric("Completed Tasks", st.session_state.get("completed_tasks", 0))

        with col4:
            st.metric("Failed Tasks", st.session_state.get("failed_tasks", 0))

        with col5:
            # Get MCP status from API
            try:
                response = httpx.get(f"{self.api_base_url}/mcp/status")
                if response.status_code == 200:
                    mcp_status = response.json()
                    status_icon = "🟢" if mcp_status.get("connected", False) else "🔴"
                    st.metric("MCP Status", f"{status_icon} {'Connected' if mcp_status.get('connected', False) else 'Offline'}")
                else:
                    st.metric("MCP Status", "🔴 Error")
            except:
                st.metric("MCP Status", "🔴 Offline")
    
    def render_sidebar(self):
        """Render the sidebar with agent information."""
        st.sidebar.title("🤖 Available Agents")

        # MCP Control Panel (Phase 2)
        with st.sidebar.expander("🔌 MCP Integration", expanded=False):
            self.render_mcp_controls()
        
        # Get agent types from API
        try:
            response = httpx.get(f"{self.api_base_url}/agents/types")
            if response.status_code == 200:
                agent_types = response.json()
                
                # Group agents by category
                core_agents = [a for a in agent_types if any(x in a for x in [
                    "layout", "component", "design_token", "typography", 
                    "color", "icon", "spacing", "border"
                ])]
                
                advanced_agents = [a for a in agent_types if any(x in a for x in [
                    "accessibility", "responsive", "animation", "prototyping",
                    "governance", "brand", "ux", "hierarchy"
                ])]
                
                technical_agents = [a for a in agent_types if any(x in a for x in [
                    "export", "code", "bridge", "version", "quality",
                    "performance", "platform", "localization"
                ])]
                
                workflow_agents = [a for a in agent_types if any(x in a for x in [
                    "project", "feedback", "documentation", "stakeholder",
                    "trend", "error", "channel"
                ])]
                
                # Display agent categories
                with st.sidebar.expander("🎯 Core Design Agents", expanded=True):
                    for agent in core_agents:
                        st.write(f"• {agent.replace('_', ' ').title()}")
                
                with st.sidebar.expander("🚀 Advanced Design Agents"):
                    for agent in advanced_agents:
                        st.write(f"• {agent.replace('_', ' ').title()}")
                
                with st.sidebar.expander("⚙️ Technical Integration Agents"):
                    for agent in technical_agents:
                        st.write(f"• {agent.replace('_', ' ').title()}")
                
                with st.sidebar.expander("🔄 Workflow & Collaboration Agents"):
                    for agent in workflow_agents:
                        st.write(f"• {agent.replace('_', ' ').title()}")
                
            else:
                st.sidebar.error("Failed to load agents")
                
        except Exception as e:
            st.sidebar.error(f"Connection error: {str(e)}")
    
    def render_design_request_form(self):
        """Render the design request form."""
        st.header("📝 Create Design Request")
        
        with st.form("design_request_form"):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                prompt = st.text_area(
                    "Design Prompt",
                    placeholder="Describe your design requirements...",
                    height=150,
                    help="Provide detailed description of what you want to design"
                )
            
            with col2:
                project_name = st.text_input(
                    "Project Name",
                    placeholder="My Design Project"
                )
                
                design_type = st.selectbox(
                    "Design Type",
                    ["Web Application", "Mobile App", "Dashboard", "Landing Page", 
                     "Component Library", "Design System", "Other"]
                )
                
                priority = st.selectbox(
                    "Priority",
                    ["Low", "Medium", "High", "Critical"],
                    index=1
                )
            
            # Additional requirements
            st.subheader("Additional Requirements")
            col3, col4 = st.columns(2)
            
            with col3:
                target_devices = st.multiselect(
                    "Target Devices",
                    ["Desktop", "Tablet", "Mobile", "Smart TV", "Wearable"],
                    default=["Desktop", "Mobile"]
                )
                
                accessibility_level = st.selectbox(
                    "Accessibility Level",
                    ["AA", "AAA"],
                    help="WCAG compliance level"
                )
            
            with col4:
                brand_colors = st.text_input(
                    "Brand Colors",
                    placeholder="#007bff, #28a745",
                    help="Comma-separated hex colors"
                )
                
                style_preferences = st.text_input(
                    "Style Preferences",
                    placeholder="Modern, minimalist, professional",
                    help="Describe your preferred design style"
                )
            
            submitted = st.form_submit_button("🚀 Create Design Request", type="primary")
            
            if submitted and prompt:
                # Create design request
                request_data = {
                    "prompt": prompt,
                    "project_name": project_name,
                    "design_type": design_type,
                    "priority": priority.lower(),
                    "requirements": {
                        "target_devices": target_devices,
                        "accessibility_level": accessibility_level,
                        "brand_colors": brand_colors.split(",") if brand_colors else [],
                        "style_preferences": style_preferences
                    }
                }
                
                # Submit request to API
                with st.spinner("Processing design request..."):
                    success = self.submit_design_request(request_data)
                    
                if success:
                    st.success("✅ Design request submitted successfully!")
                    st.rerun()
                else:
                    st.error("❌ Failed to submit design request")
            
            elif submitted:
                st.error("Please provide a design prompt")
    
    def submit_design_request(self, request_data: Dict[str, Any]) -> bool:
        """Submit design request to API."""
        try:
            response = httpx.post(
                f"{self.api_base_url}/design/request",
                json=request_data,
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # Store request in session state
                if "design_requests" not in st.session_state:
                    st.session_state.design_requests = {}
                
                st.session_state.design_requests[result["request_id"]] = result
                return True
            
            return False
            
        except Exception as e:
            st.error(f"API Error: {str(e)}")
            return False
    
    def render_active_requests(self):
        """Render active design requests."""
        st.header("📊 Active Design Requests")
        
        # Get active requests from session state
        requests = st.session_state.get("design_requests", {})
        
        if not requests:
            st.info("No active design requests. Create one above to get started!")
            return
        
        # Display requests in tabs
        request_tabs = st.tabs([f"Request {i+1}" for i in range(len(requests))])
        
        for i, (request_id, request_data) in enumerate(requests.items()):
            with request_tabs[i]:
                self.render_request_details(request_id, request_data)
    
    def render_request_details(self, request_id: str, request_data: Dict[str, Any]):
        """Render details for a specific request."""
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader(f"Request: {request_id[:8]}...")
            st.write(f"**Status:** {request_data.get('status', 'Unknown')}")
            st.write(f"**Created:** {request_data.get('created_at', 'Unknown')}")
        
        with col2:
            if st.button(f"🔄 Refresh", key=f"refresh_{request_id}"):
                self.refresh_request_status(request_id)
            
            if st.button(f"❌ Cancel", key=f"cancel_{request_id}"):
                self.cancel_request(request_id)
        
        # Show agent responses
        if "agent_responses" in request_data and request_data["agent_responses"]:
            st.subheader("🤖 Agent Responses")
            
            for response in request_data["agent_responses"]:
                with st.expander(f"{response['agent_type']} - {response['status']}"):
                    col3, col4 = st.columns(2)
                    
                    with col3:
                        st.write(f"**Status:** {response['status']}")
                        st.write(f"**Execution Time:** {response.get('execution_time', 'N/A')}s")
                    
                    with col4:
                        if response.get('error'):
                            st.error(f"Error: {response['error']}")
                        elif response.get('result'):
                            st.success("✅ Completed successfully")
                    
                    # Show result details
                    if response.get('result'):
                        st.json(response['result'])
        
        # Show consolidated result
        if "consolidated_result" in request_data and request_data["consolidated_result"]:
            st.subheader("🎯 Consolidated Design Result")
            
            result = request_data["consolidated_result"]
            
            # Display key sections
            if "consolidated_design" in result:
                with st.expander("📋 Design Specification", expanded=True):
                    st.json(result["consolidated_design"])
            
            if "implementation_plan" in result:
                with st.expander("🛠️ Implementation Plan"):
                    for step in result["implementation_plan"]:
                        st.write(f"• {step}")
            
            if "recommendations" in result:
                with st.expander("💡 Recommendations"):
                    for rec in result["recommendations"]:
                        st.write(f"• {rec}")
    
    def refresh_request_status(self, request_id: str):
        """Refresh the status of a design request."""
        try:
            response = httpx.get(f"{self.api_base_url}/design/request/{request_id}")
            
            if response.status_code == 200:
                updated_data = response.json()
                st.session_state.design_requests[request_id] = updated_data
                st.rerun()
            else:
                st.error("Failed to refresh request status")
                
        except Exception as e:
            st.error(f"Error refreshing status: {str(e)}")
    
    def cancel_request(self, request_id: str):
        """Cancel a design request."""
        try:
            response = httpx.delete(f"{self.api_base_url}/design/request/{request_id}")
            
            if response.status_code == 200:
                if request_id in st.session_state.design_requests:
                    del st.session_state.design_requests[request_id]
                st.success("Request cancelled successfully")
                st.rerun()
            else:
                st.error("Failed to cancel request")
                
        except Exception as e:
            st.error(f"Error cancelling request: {str(e)}")

    def render_mcp_controls(self):
        """Render MCP integration controls."""
        try:
            # Get MCP status
            response = httpx.get(f"{self.api_base_url}/mcp/status")
            if response.status_code == 200:
                mcp_status = response.json()

                # Display status
                if mcp_status.get("connected", False):
                    st.success("🟢 MCP Connected")
                    st.write(f"Tools Available: {mcp_status.get('tools_available', 0)}")
                    st.write(f"Figma Channel: {'Active' if mcp_status.get('figma_channel_active', False) else 'Inactive'}")

                    # MCP Tools
                    if st.button("🔧 List MCP Tools"):
                        tools_response = httpx.get(f"{self.api_base_url}/mcp/tools")
                        if tools_response.status_code == 200:
                            tools_data = tools_response.json()
                            st.json(tools_data)

                    # Figma Sync Controls
                    st.subheader("🎨 Figma Sync")

                    sync_type = st.selectbox(
                        "Sync Type",
                        ["bidirectional", "to_figma", "from_figma"],
                        key="mcp_sync_type"
                    )

                    if st.button("🔄 Test Figma Sync"):
                        test_data = {
                            "type": "test_sync",
                            "timestamp": datetime.now().isoformat(),
                            "source": "streamlit_ui"
                        }

                        sync_response = httpx.post(
                            f"{self.api_base_url}/mcp/figma/sync",
                            json={
                                "sync_type": sync_type,
                                "data": test_data,
                                "sync_mode": "update"
                            }
                        )

                        if sync_response.status_code == 200:
                            st.success("✅ Figma sync test successful")
                            st.json(sync_response.json())
                        else:
                            st.error("❌ Figma sync test failed")

                else:
                    st.error("🔴 MCP Disconnected")
                    if st.button("🔌 Initialize MCP"):
                        init_response = httpx.post(f"{self.api_base_url}/mcp/initialize")
                        if init_response.status_code == 200:
                            st.success("✅ MCP initialized successfully")
                            st.rerun()
                        else:
                            st.error("❌ MCP initialization failed")

            else:
                st.error("❌ Cannot get MCP status")

        except Exception as e:
            st.error(f"MCP Error: {str(e)}")

    def run(self):
        """Run the Streamlit application."""
        self.setup_page_config()
        self.render_header()
        self.render_sidebar()
        
        # Main content
        self.render_design_request_form()
        st.divider()
        self.render_active_requests()


def main():
    """Main function to run the Streamlit app."""
    app = StreamlitApp()
    app.run()


if __name__ == "__main__":
    main()
