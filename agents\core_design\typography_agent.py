"""Typography Specialist Agent."""

from typing import Any, Dict, List
from agents.base_agent import BaseAgent, ToolDefinition, ToolParameter
from models.schemas import AgentType, AgentTask


class TypographyAgent(BaseAgent):
    """Agent specialized in font selection, sizing, and hierarchy."""
    
    def __init__(self):
        """Initialize the Typography Agent."""
        super().__init__(AgentType.TYPOGRAPHY)
    
    @property
    def name(self) -> str:
        """Get the agent name."""
        return "Typography Specialist"
    
    @property
    def description(self) -> str:
        """Get the agent description."""
        return "Handles font selection, sizing, and typographic hierarchy for design systems"
    
    @property
    def capabilities(self) -> List[str]:
        """Get the agent capabilities."""
        return [
            "Font pairing and selection",
            "Typographic scale creation",
            "Text hierarchy definition",
            "Readability optimization",
            "Cross-platform font compatibility",
            "Accessibility compliance for text",
            "Responsive typography",
            "Brand typography guidelines"
        ]
    
    def _setup_tools(self) -> None:
        """Setup agent-specific tools."""
        # Font Selection Tool
        font_tool = self.create_tool_definition(
            name="select_font_system",
            description="Select and pair fonts for the design system",
            parameters=[
                self.create_tool_parameter(
                    "primary_font_type", "string", "Primary font category", True,
                    enum=["serif", "sans-serif", "monospace", "display"]
                ),
                self.create_tool_parameter(
                    "brand_personality", "string", "Brand personality", True
                ),
                self.create_tool_parameter(
                    "target_platforms", "array", "Target platforms", True
                )
            ]
        )
        
        # Typography Scale Tool
        scale_tool = self.create_tool_definition(
            name="create_typography_scale",
            description="Create a modular typography scale",
            parameters=[
                self.create_tool_parameter(
                    "base_font_size", "integer", "Base font size in pixels", True
                ),
                self.create_tool_parameter(
                    "scale_ratio", "number", "Scale ratio for sizing", True
                ),
                self.create_tool_parameter(
                    "scale_steps", "integer", "Number of scale steps", True
                )
            ]
        )
        
        # Text Hierarchy Tool
        hierarchy_tool = self.create_tool_definition(
            name="define_text_hierarchy",
            description="Define text hierarchy and semantic styles",
            parameters=[
                self.create_tool_parameter(
                    "content_types", "array", "Types of content", True
                ),
                self.create_tool_parameter(
                    "hierarchy_levels", "integer", "Number of hierarchy levels", True
                )
            ]
        )
        
        self.add_tool(font_tool)
        self.add_tool(scale_tool)
        self.add_tool(hierarchy_tool)
    
    async def _execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """Execute the typography design task."""
        prompt = task.prompt
        parameters = task.parameters
        
        # Analyze typography requirements
        typography_analysis = await self._analyze_typography_requirements(prompt, parameters)
        
        # Select font system
        font_system = await self._select_font_system(typography_analysis)
        
        # Create typography scale
        typography_scale = await self._create_typography_scale(typography_analysis)
        
        # Define text hierarchy
        text_hierarchy = await self._define_text_hierarchy(typography_analysis)
        
        # Generate CSS specifications
        css_specifications = await self._generate_typography_css(
            font_system, typography_scale, text_hierarchy
        )
        
        return {
            "typography_analysis": typography_analysis,
            "font_system": font_system,
            "typography_scale": typography_scale,
            "text_hierarchy": text_hierarchy,
            "css_specifications": css_specifications,
            "accessibility_notes": await self._generate_accessibility_notes(),
            "implementation_guide": await self._generate_implementation_guide()
        }
    
    async def _analyze_typography_requirements(
        self, prompt: str, parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze typography requirements from the prompt."""
        return {
            "brand_personality": parameters.get("brand_personality", "professional"),
            "target_platforms": parameters.get("target_platforms", ["web", "mobile"]),
            "content_types": parameters.get("content_types", ["headings", "body", "captions"]),
            "accessibility_level": parameters.get("accessibility_level", "AA"),
            "language_support": parameters.get("language_support", ["latin"]),
            "reading_context": parameters.get("reading_context", "screen")
        }
    
    async def _select_font_system(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Select appropriate fonts for the design system."""
        return {
            "primary_font": {
                "name": "Inter",
                "category": "sans-serif",
                "weights": [300, 400, 500, 600, 700],
                "styles": ["normal", "italic"],
                "fallback": "system-ui, -apple-system, sans-serif",
                "usage": "headings, UI elements"
            },
            "secondary_font": {
                "name": "Source Sans Pro",
                "category": "sans-serif", 
                "weights": [400, 600],
                "styles": ["normal", "italic"],
                "fallback": "Arial, sans-serif",
                "usage": "body text, paragraphs"
            },
            "monospace_font": {
                "name": "JetBrains Mono",
                "category": "monospace",
                "weights": [400, 500],
                "styles": ["normal"],
                "fallback": "Consolas, Monaco, monospace",
                "usage": "code, technical content"
            },
            "font_loading": {
                "strategy": "swap",
                "preload": ["Inter-400", "Inter-600"],
                "display": "swap"
            }
        }
    
    async def _create_typography_scale(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create a modular typography scale."""
        base_size = 16  # Base font size in pixels
        ratio = 1.25    # Major third scale
        
        return {
            "base_size": base_size,
            "scale_ratio": ratio,
            "scale": {
                "xs": round(base_size / (ratio ** 2)),      # 10px
                "sm": round(base_size / ratio),             # 13px
                "base": base_size,                          # 16px
                "lg": round(base_size * ratio),             # 20px
                "xl": round(base_size * (ratio ** 2)),      # 25px
                "2xl": round(base_size * (ratio ** 3)),     # 31px
                "3xl": round(base_size * (ratio ** 4)),     # 39px
                "4xl": round(base_size * (ratio ** 5)),     # 49px
                "5xl": round(base_size * (ratio ** 6)),     # 61px
                "6xl": round(base_size * (ratio ** 7))      # 76px
            },
            "line_heights": {
                "tight": 1.25,
                "normal": 1.5,
                "relaxed": 1.75,
                "loose": 2.0
            },
            "letter_spacing": {
                "tight": "-0.025em",
                "normal": "0",
                "wide": "0.025em",
                "wider": "0.05em",
                "widest": "0.1em"
            }
        }
    
    async def _define_text_hierarchy(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Define text hierarchy and semantic styles."""
        return {
            "headings": {
                "h1": {
                    "font_size": "3xl",
                    "font_weight": 700,
                    "line_height": "tight",
                    "letter_spacing": "tight",
                    "margin_bottom": "1rem"
                },
                "h2": {
                    "font_size": "2xl",
                    "font_weight": 600,
                    "line_height": "tight",
                    "letter_spacing": "tight",
                    "margin_bottom": "0.75rem"
                },
                "h3": {
                    "font_size": "xl",
                    "font_weight": 600,
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "margin_bottom": "0.5rem"
                },
                "h4": {
                    "font_size": "lg",
                    "font_weight": 500,
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "margin_bottom": "0.5rem"
                }
            },
            "body_text": {
                "paragraph": {
                    "font_size": "base",
                    "font_weight": 400,
                    "line_height": "relaxed",
                    "letter_spacing": "normal",
                    "margin_bottom": "1rem"
                },
                "lead": {
                    "font_size": "lg",
                    "font_weight": 400,
                    "line_height": "relaxed",
                    "letter_spacing": "normal",
                    "margin_bottom": "1.5rem"
                },
                "small": {
                    "font_size": "sm",
                    "font_weight": 400,
                    "line_height": "normal",
                    "letter_spacing": "normal"
                }
            },
            "ui_elements": {
                "button": {
                    "font_size": "base",
                    "font_weight": 500,
                    "line_height": "normal",
                    "letter_spacing": "wide",
                    "text_transform": "none"
                },
                "label": {
                    "font_size": "sm",
                    "font_weight": 500,
                    "line_height": "normal",
                    "letter_spacing": "wide",
                    "text_transform": "uppercase"
                },
                "caption": {
                    "font_size": "xs",
                    "font_weight": 400,
                    "line_height": "normal",
                    "letter_spacing": "normal"
                }
            }
        }
    
    async def _generate_typography_css(
        self, 
        font_system: Dict[str, Any],
        typography_scale: Dict[str, Any],
        text_hierarchy: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate CSS specifications for typography."""
        css_vars = {}
        
        # Font family variables
        css_vars["--font-primary"] = f"{font_system['primary_font']['name']}, {font_system['primary_font']['fallback']}"
        css_vars["--font-secondary"] = f"{font_system['secondary_font']['name']}, {font_system['secondary_font']['fallback']}"
        css_vars["--font-mono"] = f"{font_system['monospace_font']['name']}, {font_system['monospace_font']['fallback']}"
        
        # Font size variables
        for size_name, size_value in typography_scale["scale"].items():
            css_vars[f"--text-{size_name}"] = f"{size_value}px"
        
        # Line height variables
        for lh_name, lh_value in typography_scale["line_heights"].items():
            css_vars[f"--leading-{lh_name}"] = str(lh_value)
        
        # Letter spacing variables
        for ls_name, ls_value in typography_scale["letter_spacing"].items():
            css_vars[f"--tracking-{ls_name}"] = ls_value
        
        return {
            "css_variables": css_vars,
            "font_imports": [
                "@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');",
                "@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap');",
                "@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap');"
            ],
            "utility_classes": [
                ".text-xs", ".text-sm", ".text-base", ".text-lg", ".text-xl",
                ".font-light", ".font-normal", ".font-medium", ".font-semibold", ".font-bold",
                ".leading-tight", ".leading-normal", ".leading-relaxed", ".leading-loose"
            ]
        }
    
    async def _generate_accessibility_notes(self) -> List[str]:
        """Generate typography accessibility notes."""
        return [
            "Maintain minimum 16px font size for body text",
            "Ensure sufficient color contrast ratios (4.5:1 for normal text)",
            "Use relative units (rem/em) for scalable typography",
            "Provide adequate line spacing (1.5x font size minimum)",
            "Test with screen readers and zoom up to 200%",
            "Avoid justified text alignment for better readability"
        ]
    
    async def _generate_implementation_guide(self) -> List[str]:
        """Generate typography implementation guide."""
        return [
            "Load fonts using font-display: swap for better performance",
            "Implement font fallbacks for graceful degradation",
            "Use CSS custom properties for consistent scaling",
            "Test typography across different devices and browsers",
            "Consider variable fonts for better performance",
            "Implement proper font loading strategies"
        ]
