"""
Figma Sync Agent with MCP Integration.

This agent handles real-time synchronization with Figma using the MCP server,
following OpenAI SDK Tools patterns for seamless integration.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from agents.base_agent import BaseAgent, ToolDefinition, ToolParameter
from models.schemas import AgentType, AgentTask, AgentResponse
from services.mcp_service import mcp_service

logger = logging.getLogger(__name__)


class FigmaSyncAgent(BaseAgent):
    """Agent for real-time Figma synchronization using MCP."""
    
    def __init__(self):
        super().__init__(AgentType.FIGMA_EXPORT_MANAGER)
        self.mcp_tools: List[Dict[str, Any]] = []
        
    def _setup_tools(self):
        """Setup MCP-integrated tools for Figma synchronization."""
        
        # Tool 1: Sync Design to Figma
        self.add_tool(ToolDefinition(
            name="sync_design_to_figma",
            description="Synchronize design data to Figma using MCP",
            parameters=[
                ToolParameter(
                    name="design_data",
                    type="object",
                    description="Design data to sync to Figma",
                    required=True
                ),
                ToolParameter(
                    name="sync_mode",
                    type="string",
                    description="Sync mode: 'create', 'update', or 'replace'",
                    required=False
                )
            ]
        ))
        
        # Tool 2: Fetch Design from Figma
        self.add_tool(ToolDefinition(
            name="fetch_design_from_figma",
            description="Fetch design data from Figma using MCP",
            parameters=[
                ToolParameter(
                    name="file_id",
                    type="string",
                    description="Figma file ID to fetch from",
                    required=False
                ),
                ToolParameter(
                    name="node_id",
                    type="string",
                    description="Specific node ID to fetch",
                    required=False
                )
            ]
        ))
        
        # Tool 3: Real-time Sync Status
        self.add_tool(ToolDefinition(
            name="get_sync_status",
            description="Get real-time synchronization status with Figma",
            parameters=[
                ToolParameter(
                    name="channel_id",
                    type="string",
                    description="Plugin channel ID to check",
                    required=False
                )
            ]
        ))
        
        # Tool 4: Send Command to Figma Plugin
        self.add_tool(ToolDefinition(
            name="send_figma_command",
            description="Send command to Figma plugin via MCP",
            parameters=[
                ToolParameter(
                    name="command",
                    type="string",
                    description="Command to send to Figma plugin",
                    required=True
                ),
                ToolParameter(
                    name="payload",
                    type="object",
                    description="Command payload data",
                    required=False
                )
            ]
        ))
    
    async def _execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """Execute Figma sync task using MCP integration."""
        try:
            logger.info(f"🔄 Executing Figma sync task: {task.description}")
            
            # Initialize MCP service if not already done
            if not mcp_service.is_connected:
                await mcp_service.initialize()
            
            # Refresh available MCP tools
            self.mcp_tools = await mcp_service.get_figma_tools()
            
            # Analyze task requirements
            sync_plan = await self._analyze_sync_requirements(task)
            
            # Execute sync operations
            results = await self._execute_sync_operations(sync_plan)
            
            # Verify sync status
            status = await self._verify_sync_status()
            
            return {
                "status": "completed",
                "agent_type": self.agent_type.value,
                "sync_plan": sync_plan,
                "sync_results": results,
                "sync_status": status,
                "mcp_tools_available": len(self.mcp_tools),
                "execution_time": (datetime.now() - self.execution_start_time).total_seconds(),
                "figma_integration": {
                    "mcp_connected": mcp_service.is_connected,
                    "tools_count": len(self.mcp_tools),
                    "channel_active": True
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Figma sync task failed: {e}")
            return {
                "status": "failed",
                "agent_type": self.agent_type.value,
                "error": str(e),
                "mcp_status": mcp_service.is_connected
            }
    
    async def _analyze_sync_requirements(self, task: AgentTask) -> Dict[str, Any]:
        """Analyze task requirements for Figma synchronization."""
        try:
            # Extract sync requirements from task
            requirements = {
                "sync_type": "bidirectional",  # Default to bidirectional sync
                "target_elements": [],
                "sync_mode": "update",
                "priority": task.priority,
                "real_time": True
            }
            
            # Parse task description for specific requirements
            description = task.description.lower()
            
            if "create" in description:
                requirements["sync_mode"] = "create"
            elif "update" in description:
                requirements["sync_mode"] = "update"
            elif "replace" in description:
                requirements["sync_mode"] = "replace"
            
            if "component" in description:
                requirements["target_elements"].append("components")
            if "color" in description or "palette" in description:
                requirements["target_elements"].append("colors")
            if "typography" in description or "text" in description:
                requirements["target_elements"].append("typography")
            if "layout" in description or "grid" in description:
                requirements["target_elements"].append("layout")
            
            logger.info(f"📋 Sync requirements analyzed: {requirements}")
            return requirements
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze sync requirements: {e}")
            return {"sync_type": "basic", "sync_mode": "update"}
    
    async def _execute_sync_operations(self, sync_plan: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute synchronization operations with Figma."""
        results = []
        
        try:
            # Operation 1: Send design data to Figma
            if sync_plan.get("sync_mode") in ["create", "update", "replace"]:
                sync_result = await self._sync_to_figma(sync_plan)
                results.append(sync_result)
            
            # Operation 2: Fetch current state from Figma
            if sync_plan.get("sync_type") == "bidirectional":
                fetch_result = await self._fetch_from_figma()
                results.append(fetch_result)
            
            # Operation 3: Send plugin commands if needed
            if sync_plan.get("target_elements"):
                command_result = await self._send_plugin_commands(sync_plan)
                results.append(command_result)
            
            logger.info(f"✅ Completed {len(results)} sync operations")
            return results
            
        except Exception as e:
            logger.error(f"❌ Sync operations failed: {e}")
            return [{"status": "failed", "error": str(e)}]
    
    async def _sync_to_figma(self, sync_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Sync design data to Figma using MCP."""
        try:
            # Prepare design data for Figma
            design_data = {
                "type": "design_sync",
                "mode": sync_plan.get("sync_mode", "update"),
                "elements": sync_plan.get("target_elements", []),
                "timestamp": datetime.now().isoformat(),
                "agent": self.agent_type.value
            }
            
            # Send to Figma via MCP
            result = await mcp_service.send_to_figma("pmdbxbo8", design_data)
            
            return {
                "operation": "sync_to_figma",
                "status": "success",
                "data_sent": design_data,
                "figma_response": result
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to sync to Figma: {e}")
            return {
                "operation": "sync_to_figma",
                "status": "failed",
                "error": str(e)
            }
    
    async def _fetch_from_figma(self) -> Dict[str, Any]:
        """Fetch design data from Figma using MCP."""
        try:
            # Receive data from Figma via MCP
            result = await mcp_service.receive_from_figma("pmdbxbo8")
            
            return {
                "operation": "fetch_from_figma",
                "status": "success",
                "figma_data": result
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch from Figma: {e}")
            return {
                "operation": "fetch_from_figma",
                "status": "failed",
                "error": str(e)
            }
    
    async def _send_plugin_commands(self, sync_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Send commands to Figma plugin via MCP."""
        try:
            commands = []
            
            # Generate commands based on target elements
            for element in sync_plan.get("target_elements", []):
                command = {
                    "action": f"sync_{element}",
                    "mode": sync_plan.get("sync_mode", "update"),
                    "timestamp": datetime.now().isoformat()
                }
                commands.append(command)
            
            # Send commands via MCP
            results = []
            for command in commands:
                result = await mcp_service.call_tool("send_figma_command", {
                    "command": command["action"],
                    "payload": command
                })
                results.append(result)
            
            return {
                "operation": "send_plugin_commands",
                "status": "success",
                "commands_sent": len(commands),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to send plugin commands: {e}")
            return {
                "operation": "send_plugin_commands",
                "status": "failed",
                "error": str(e)
            }
    
    async def _verify_sync_status(self) -> Dict[str, Any]:
        """Verify synchronization status with Figma."""
        try:
            # Check MCP connection status
            mcp_status = {
                "connected": mcp_service.is_connected,
                "tools_available": len(self.mcp_tools)
            }
            
            # Get sync status via MCP
            sync_status = await mcp_service.call_tool("get_sync_status", {
                "channel_id": "pmdbxbo8"
            })
            
            return {
                "mcp_status": mcp_status,
                "figma_sync_status": sync_status,
                "overall_status": "connected" if mcp_service.is_connected else "disconnected"
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to verify sync status: {e}")
            return {
                "overall_status": "error",
                "error": str(e)
            }
