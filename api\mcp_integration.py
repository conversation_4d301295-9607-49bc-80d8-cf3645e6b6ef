"""
MCP Integration Layer for FastAPI.

This module provides MCP integration endpoints and middleware for the FastAPI application,
enabling real-time Figma synchronization through the MCP server.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel

from services.mcp_service import mcp_service
from models.schemas import AgentTask, AgentResponse

logger = logging.getLogger(__name__)

# Create MCP router
mcp_router = APIRouter(prefix="/mcp", tags=["MCP Integration"])


class MCPConnectionStatus(BaseModel):
    """MCP connection status response model."""
    connected: bool
    server_running: bool
    tools_available: int
    figma_channel_active: bool
    last_sync: Optional[str] = None


class FigmaSyncRequest(BaseModel):
    """Request model for Figma synchronization."""
    sync_type: str = "bidirectional"  # "to_figma", "from_figma", "bidirectional"
    data: Dict[str, Any]
    channel_id: Optional[str] = None
    sync_mode: str = "update"  # "create", "update", "replace"


class FigmaSyncResponse(BaseModel):
    """Response model for Figma synchronization."""
    status: str
    sync_id: str
    results: List[Dict[str, Any]]
    figma_response: Optional[Dict[str, Any]] = None


class MCPToolCall(BaseModel):
    """Request model for MCP tool calls."""
    tool_name: str
    arguments: Dict[str, Any]


class MCPToolResponse(BaseModel):
    """Response model for MCP tool calls."""
    status: str
    tool_name: str
    result: Dict[str, Any]
    execution_time: float


@mcp_router.get("/status", response_model=MCPConnectionStatus)
async def get_mcp_status():
    """Get MCP connection and service status."""
    try:
        # Check MCP service status
        tools = await mcp_service.get_figma_tools() if mcp_service.is_connected else []
        
        return MCPConnectionStatus(
            connected=mcp_service.is_connected,
            server_running=mcp_service.server_process is not None and mcp_service.server_process.poll() is None,
            tools_available=len(tools),
            figma_channel_active=True,  # Assume active if connected
            last_sync=None  # TODO: Track last sync time
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to get MCP status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get MCP status: {str(e)}")


@mcp_router.post("/initialize")
async def initialize_mcp():
    """Initialize MCP service and connect to Figma server."""
    try:
        logger.info("🔌 Initializing MCP service via API...")
        
        success = await mcp_service.initialize()
        
        if success:
            tools = await mcp_service.get_figma_tools()
            return {
                "status": "success",
                "message": "MCP service initialized successfully",
                "tools_available": len(tools),
                "connected": mcp_service.is_connected
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to initialize MCP service")
            
    except Exception as e:
        logger.error(f"❌ MCP initialization failed: {e}")
        raise HTTPException(status_code=500, detail=f"MCP initialization failed: {str(e)}")


@mcp_router.get("/tools")
async def list_mcp_tools():
    """List all available MCP tools."""
    try:
        if not mcp_service.is_connected:
            raise HTTPException(status_code=503, detail="MCP service not connected")
        
        tools = await mcp_service.get_figma_tools()
        
        return {
            "status": "success",
            "tools_count": len(tools),
            "tools": tools
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to list MCP tools: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list MCP tools: {str(e)}")


@mcp_router.post("/tools/call", response_model=MCPToolResponse)
async def call_mcp_tool(request: MCPToolCall):
    """Call a specific MCP tool."""
    try:
        if not mcp_service.is_connected:
            raise HTTPException(status_code=503, detail="MCP service not connected")
        
        import time
        start_time = time.time()
        
        # Call the MCP tool
        result = await mcp_service.call_tool(request.tool_name, request.arguments)
        
        execution_time = time.time() - start_time
        
        return MCPToolResponse(
            status="success",
            tool_name=request.tool_name,
            result=result,
            execution_time=execution_time
        )
        
    except Exception as e:
        logger.error(f"❌ MCP tool call failed: {e}")
        raise HTTPException(status_code=500, detail=f"MCP tool call failed: {str(e)}")


@mcp_router.post("/figma/sync", response_model=FigmaSyncResponse)
async def sync_with_figma(request: FigmaSyncRequest, background_tasks: BackgroundTasks):
    """Synchronize data with Figma through MCP."""
    try:
        if not mcp_service.is_connected:
            raise HTTPException(status_code=503, detail="MCP service not connected")
        
        import uuid
        sync_id = str(uuid.uuid4())
        
        logger.info(f"🔄 Starting Figma sync: {sync_id}")
        
        results = []
        figma_response = None
        
        # Execute sync based on type
        if request.sync_type in ["to_figma", "bidirectional"]:
            # Send data to Figma
            channel_id = request.channel_id or "oa34ym6m"
            figma_response = await mcp_service.send_to_figma(channel_id, request.data)
            results.append({
                "operation": "send_to_figma",
                "status": "success",
                "channel_id": channel_id
            })
        
        if request.sync_type in ["from_figma", "bidirectional"]:
            # Receive data from Figma
            channel_id = request.channel_id or "oa34ym6m"
            figma_data = await mcp_service.receive_from_figma(channel_id)
            results.append({
                "operation": "receive_from_figma",
                "status": "success",
                "channel_id": channel_id,
                "data": figma_data
            })
        
        return FigmaSyncResponse(
            status="completed",
            sync_id=sync_id,
            results=results,
            figma_response=figma_response
        )
        
    except Exception as e:
        logger.error(f"❌ Figma sync failed: {e}")
        raise HTTPException(status_code=500, detail=f"Figma sync failed: {str(e)}")


@mcp_router.post("/figma/send")
async def send_to_figma(data: Dict[str, Any], channel_id: Optional[str] = None):
    """Send data to Figma plugin."""
    try:
        if not mcp_service.is_connected:
            raise HTTPException(status_code=503, detail="MCP service not connected")
        
        active_channel = channel_id or "pmdbxbo8"
        result = await mcp_service.send_to_figma(active_channel, data)
        
        return {
            "status": "success",
            "channel_id": active_channel,
            "figma_response": result
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to send to Figma: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to send to Figma: {str(e)}")


@mcp_router.get("/figma/receive")
async def receive_from_figma(channel_id: Optional[str] = None):
    """Receive data from Figma plugin."""
    try:
        if not mcp_service.is_connected:
            raise HTTPException(status_code=503, detail="MCP service not connected")
        
        active_channel = channel_id or "pmdbxbo8"
        result = await mcp_service.receive_from_figma(active_channel)
        
        return {
            "status": "success",
            "channel_id": active_channel,
            "figma_data": result
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to receive from Figma: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to receive from Figma: {str(e)}")


@mcp_router.post("/shutdown")
async def shutdown_mcp():
    """Shutdown MCP service."""
    try:
        await mcp_service.shutdown()
        
        return {
            "status": "success",
            "message": "MCP service shutdown successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ MCP shutdown failed: {e}")
        raise HTTPException(status_code=500, detail=f"MCP shutdown failed: {str(e)}")


# Background task for MCP health monitoring
async def monitor_mcp_health():
    """Background task to monitor MCP service health."""
    while True:
        try:
            if mcp_service.is_connected:
                # Refresh tools periodically
                await mcp_service._refresh_tools()
                logger.debug("🔍 MCP health check completed")
            
            await asyncio.sleep(30)  # Check every 30 seconds
            
        except Exception as e:
            logger.error(f"❌ MCP health check failed: {e}")
            await asyncio.sleep(60)  # Wait longer on error
