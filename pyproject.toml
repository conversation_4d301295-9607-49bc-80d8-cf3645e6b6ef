[project]
name = "figma-agent-system"
version = "1.0.0"
description = "Streamlit-FastAPI Multi-Agent Figma Design System with OpenAI SDK Architecture"
authors = [
    {name = "Figma Agent System", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "streamlit>=1.28.0",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "httpx>=0.25.0",
    "websockets>=12.0",
    "google-generativeai>=0.3.0",
    "openai>=1.3.0",
    "asyncio-mqtt>=0.13.0",
    "python-multipart>=0.0.6",
    "jinja2>=3.1.2",
    "aiofiles>=23.2.1",
    "typing-extensions>=4.8.0",
    "pydantic-settings>=2.1.0",
    "rich>=13.7.0",
    "loguru>=0.7.2",
    "openai-agents>=0.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.7.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = [
    "agents",
    "api",
    "config",
    "frontend",
    "models",
    "router",
    "services"
]

[tool.uv]
dev-dependencies = []

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
