"""Agent registry for managing all 31 specialized agents."""

from typing import Any, Dict, List, Type
from agents.base_agent import BaseAgent
from models.schemas import AgentType

# Import all agent classes
from agents.core_design.layout_grid_agent import LayoutGridAgent
from agents.core_design.color_palette_agent import ColorPaletteAgent
from agents.core_design.typography_agent import TypographyAgent
from agents.core_design.component_creator_agent import ComponentCreatorAgent


class AgentRegistry:
    """Registry for managing all specialized agents."""
    
    def __init__(self):
        """Initialize the agent registry."""
        self._agents: Dict[AgentType, BaseAgent] = {}
        self._agent_classes: Dict[AgentType, Type[BaseAgent]] = {}
        self._register_agents()
    
    def _register_agents(self) -> None:
        """Register all agent classes."""
        # Core Design Agents
        self._agent_classes[AgentType.LAYOUT_GRID] = LayoutGridAgent
        self._agent_classes[AgentType.COLOR_PALETTE] = ColorPaletteAgent
        self._agent_classes[AgentType.TYPOGRAPHY] = TypographyAgent
        self._agent_classes[AgentType.COMPONENT_CREATOR] = ComponentCreatorAgent
        
        # TODO: Add remaining 29 agents
        # For now, we'll create placeholder agents for the remaining types
        self._register_placeholder_agents()
    
    def _register_placeholder_agents(self) -> None:
        """Register placeholder agents for remaining agent types."""
        # This will be replaced with actual agent implementations
        placeholder_types = [
            AgentType.DESIGN_TOKEN,
            AgentType.ICON_ASSET,
            AgentType.SPACING_ALIGNMENT,
            AgentType.BORDER_SHADOW,
            AgentType.ACCESSIBILITY,
            AgentType.RESPONSIVE,
            AgentType.ANIMATION,
            AgentType.PROTOTYPING,
            AgentType.DESIGN_GOVERNANCE,
            AgentType.BRAND_GUIDELINES,
            AgentType.UX_VALIDATOR,
            AgentType.VISUAL_HIERARCHY,
            AgentType.EXPORT_OPTIMIZER,
            AgentType.CODE_GENERATION,
            AgentType.DESIGN_DEV_BRIDGE,
            AgentType.VERSION_CONTROL,
            AgentType.QUALITY_ASSURANCE,
            AgentType.PERFORMANCE,
            AgentType.CROSS_PLATFORM,
            AgentType.LOCALIZATION,
            AgentType.PROJECT_MANAGER,
            AgentType.FEEDBACK_COLLECTOR,
            AgentType.DOCUMENTATION,
            AgentType.STAKEHOLDER_COMM,
            AgentType.TREND_ANALYZER,
            AgentType.ERROR_HANDLER,
            AgentType.CHANNEL_CONNECTOR,
        ]
        
        for agent_type in placeholder_types:
            self._agent_classes[agent_type] = self._create_placeholder_agent_class(agent_type)
    
    def _create_placeholder_agent_class(self, agent_type: AgentType) -> Type[BaseAgent]:
        """Create a placeholder agent class for development."""
        
        class PlaceholderAgent(BaseAgent):
            def __init__(self):
                super().__init__(agent_type)
            
            @property
            def name(self) -> str:
                return f"Placeholder {agent_type.value.replace('_', ' ').title()}"
            
            @property
            def description(self) -> str:
                return f"Placeholder agent for {agent_type.value}"
            
            @property
            def capabilities(self) -> List[str]:
                return [f"Placeholder capability for {agent_type.value}"]
            
            def _setup_tools(self) -> None:
                # Add a basic placeholder tool
                tool = self.create_tool_definition(
                    name=f"placeholder_{agent_type.value}",
                    description=f"Placeholder tool for {agent_type.value}",
                    parameters=[
                        self.create_tool_parameter(
                            "input", "string", "Input parameter", True
                        )
                    ]
                )
                self.add_tool(tool)
            
            async def _execute_task(self, task) -> Dict[str, Any]:
                return {
                    "status": "placeholder_completed",
                    "agent_type": agent_type.value,
                    "message": f"Placeholder execution for {agent_type.value}",
                    "task_id": task.task_id,
                    "placeholder_result": {
                        "simulated_output": f"Simulated result from {agent_type.value}",
                        "execution_time": 1.0,
                        "success": True
                    }
                }
        
        return PlaceholderAgent
    
    def get_agent(self, agent_type: AgentType) -> BaseAgent:
        """Get an agent instance by type."""
        if agent_type not in self._agents:
            if agent_type not in self._agent_classes:
                raise ValueError(f"Unknown agent type: {agent_type}")
            
            # Create new agent instance
            agent_class = self._agent_classes[agent_type]
            self._agents[agent_type] = agent_class()
        
        return self._agents[agent_type]
    
    def get_all_agents(self) -> Dict[AgentType, BaseAgent]:
        """Get all agent instances."""
        # Ensure all agents are instantiated
        for agent_type in self._agent_classes:
            self.get_agent(agent_type)
        
        return self._agents.copy()
    
    def get_available_agent_types(self) -> List[AgentType]:
        """Get list of all available agent types."""
        return list(self._agent_classes.keys())
    
    def get_agent_capabilities(self) -> Dict[AgentType, List[str]]:
        """Get capabilities for all agents."""
        capabilities = {}
        for agent_type in self._agent_classes:
            agent = self.get_agent(agent_type)
            capabilities[agent_type] = agent.capabilities
        return capabilities
    
    def get_agent_tools(self) -> Dict[AgentType, List[str]]:
        """Get tool names for all agents."""
        tools = {}
        for agent_type in self._agent_classes:
            agent = self.get_agent(agent_type)
            tools[agent_type] = [tool.name for tool in agent.get_tool_definitions()]
        return tools
    
    def validate_agent_type(self, agent_type: AgentType) -> bool:
        """Validate if an agent type is available."""
        return agent_type in self._agent_classes
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        total_agents = len(self._agent_classes)
        active_agents = len(self._agents)
        
        agent_statuses = {}
        for agent_type, agent in self._agents.items():
            agent_statuses[agent_type.value] = agent.get_status()
        
        return {
            "total_agents": total_agents,
            "active_agents": active_agents,
            "agent_statuses": agent_statuses,
            "available_types": [t.value for t in self._agent_classes.keys()]
        }


# Global agent registry instance
agent_registry = AgentRegistry()
