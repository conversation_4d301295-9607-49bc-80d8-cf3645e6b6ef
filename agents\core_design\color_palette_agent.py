"""Color Palette Generator Agent."""

from typing import Any, Dict, List
from agents.base_agent import BaseAgent, ToolDefinition, ToolParameter
from models.schemas import AgentType, AgentTask


class ColorPaletteAgent(BaseAgent):
    """Agent specialized in creating cohesive color schemes."""
    
    def __init__(self):
        """Initialize the Color Palette Agent."""
        super().__init__(AgentType.COLOR_PALETTE)
    
    @property
    def name(self) -> str:
        """Get the agent name."""
        return "Color Palette Generator"
    
    @property
    def description(self) -> str:
        """Get the agent description."""
        return "Creates cohesive color schemes and palettes for design systems"
    
    @property
    def capabilities(self) -> List[str]:
        """Get the agent capabilities."""
        return [
            "Color theory application",
            "Brand-aligned color palette creation",
            "Accessibility-compliant color combinations",
            "Color psychology and emotional impact",
            "Semantic color system design",
            "Dark and light theme variations",
            "Color contrast ratio validation",
            "Color harmony and balance"
        ]
    
    def _setup_tools(self) -> None:
        """Setup agent-specific tools."""
        # Primary Palette Tool
        primary_tool = self.create_tool_definition(
            name="create_primary_palette",
            description="Create primary color palette based on brand and requirements",
            parameters=[
                self.create_tool_parameter(
                    "brand_color", "string", "Primary brand color (hex)", True
                ),
                self.create_tool_parameter(
                    "color_harmony", "string", "Color harmony type", True,
                    enum=["monochromatic", "analogous", "complementary", "triadic", "tetradic"]
                ),
                self.create_tool_parameter(
                    "palette_size", "integer", "Number of colors in palette", True
                )
            ]
        )
        
        # Semantic Colors Tool
        semantic_tool = self.create_tool_definition(
            name="define_semantic_colors",
            description="Define semantic color system for UI states and feedback",
            parameters=[
                self.create_tool_parameter(
                    "success_color", "string", "Success state color", False
                ),
                self.create_tool_parameter(
                    "warning_color", "string", "Warning state color", False
                ),
                self.create_tool_parameter(
                    "error_color", "string", "Error state color", False
                ),
                self.create_tool_parameter(
                    "info_color", "string", "Info state color", False
                )
            ]
        )
        
        # Accessibility Validation Tool
        accessibility_tool = self.create_tool_definition(
            name="validate_accessibility",
            description="Validate color combinations for accessibility compliance",
            parameters=[
                self.create_tool_parameter(
                    "wcag_level", "string", "WCAG compliance level", True,
                    enum=["AA", "AAA"]
                ),
                self.create_tool_parameter(
                    "color_combinations", "array", "Color combinations to validate", True
                )
            ]
        )
        
        self.add_tool(primary_tool)
        self.add_tool(semantic_tool)
        self.add_tool(accessibility_tool)
    
    async def _execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """Execute the color palette generation task."""
        prompt = task.prompt
        parameters = task.parameters
        
        # Analyze color requirements
        color_analysis = await self._analyze_color_requirements(prompt, parameters)
        
        # Create primary palette
        primary_palette = await self._create_primary_palette(color_analysis)
        
        # Define semantic colors
        semantic_colors = await self._define_semantic_colors(color_analysis)
        
        # Create neutral palette
        neutral_palette = await self._create_neutral_palette(color_analysis)
        
        # Generate theme variations
        theme_variations = await self._generate_theme_variations(
            primary_palette, semantic_colors, neutral_palette
        )
        
        # Validate accessibility
        accessibility_report = await self._validate_accessibility(
            primary_palette, semantic_colors, neutral_palette
        )
        
        return {
            "color_analysis": color_analysis,
            "primary_palette": primary_palette,
            "semantic_colors": semantic_colors,
            "neutral_palette": neutral_palette,
            "theme_variations": theme_variations,
            "accessibility_report": accessibility_report,
            "css_variables": await self._generate_css_variables(
                primary_palette, semantic_colors, neutral_palette
            ),
            "usage_guidelines": await self._generate_usage_guidelines()
        }
    
    async def _analyze_color_requirements(
        self, prompt: str, parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze color requirements from the prompt."""
        return {
            "brand_color": parameters.get("brand_color", "#007bff"),
            "color_harmony": parameters.get("color_harmony", "analogous"),
            "target_audience": parameters.get("target_audience", "general"),
            "industry": parameters.get("industry", "technology"),
            "emotional_tone": parameters.get("emotional_tone", "professional"),
            "accessibility_level": parameters.get("accessibility_level", "AA")
        }
    
    async def _create_primary_palette(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create the primary color palette."""
        brand_color = analysis["brand_color"]
        
        return {
            "primary": {
                "50": "#e3f2fd",
                "100": "#bbdefb", 
                "200": "#90caf9",
                "300": "#64b5f6",
                "400": "#42a5f5",
                "500": brand_color,
                "600": "#1e88e5",
                "700": "#1976d2",
                "800": "#1565c0",
                "900": "#0d47a1"
            },
            "secondary": {
                "50": "#f3e5f5",
                "100": "#e1bee7",
                "200": "#ce93d8",
                "300": "#ba68c8",
                "400": "#ab47bc",
                "500": "#9c27b0",
                "600": "#8e24aa",
                "700": "#7b1fa2",
                "800": "#6a1b9a",
                "900": "#4a148c"
            },
            "accent": {
                "50": "#fff3e0",
                "100": "#ffe0b2",
                "200": "#ffcc80",
                "300": "#ffb74d",
                "400": "#ffa726",
                "500": "#ff9800",
                "600": "#fb8c00",
                "700": "#f57c00",
                "800": "#ef6c00",
                "900": "#e65100"
            }
        }
    
    async def _define_semantic_colors(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Define semantic color system."""
        return {
            "success": {
                "light": "#d4edda",
                "main": "#28a745",
                "dark": "#155724",
                "contrast": "#ffffff"
            },
            "warning": {
                "light": "#fff3cd",
                "main": "#ffc107",
                "dark": "#856404",
                "contrast": "#212529"
            },
            "error": {
                "light": "#f8d7da",
                "main": "#dc3545",
                "dark": "#721c24",
                "contrast": "#ffffff"
            },
            "info": {
                "light": "#d1ecf1",
                "main": "#17a2b8",
                "dark": "#0c5460",
                "contrast": "#ffffff"
            }
        }
    
    async def _create_neutral_palette(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create neutral color palette."""
        return {
            "neutral": {
                "0": "#ffffff",
                "50": "#fafafa",
                "100": "#f5f5f5",
                "200": "#eeeeee",
                "300": "#e0e0e0",
                "400": "#bdbdbd",
                "500": "#9e9e9e",
                "600": "#757575",
                "700": "#616161",
                "800": "#424242",
                "900": "#212121",
                "1000": "#000000"
            }
        }
    
    async def _generate_theme_variations(
        self, 
        primary: Dict[str, Any],
        semantic: Dict[str, Any],
        neutral: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate light and dark theme variations."""
        return {
            "light": {
                "background": {
                    "primary": neutral["neutral"]["0"],
                    "secondary": neutral["neutral"]["50"],
                    "tertiary": neutral["neutral"]["100"]
                },
                "text": {
                    "primary": neutral["neutral"]["900"],
                    "secondary": neutral["neutral"]["700"],
                    "disabled": neutral["neutral"]["400"]
                },
                "surface": {
                    "primary": neutral["neutral"]["0"],
                    "secondary": neutral["neutral"]["50"]
                }
            },
            "dark": {
                "background": {
                    "primary": neutral["neutral"]["900"],
                    "secondary": neutral["neutral"]["800"],
                    "tertiary": neutral["neutral"]["700"]
                },
                "text": {
                    "primary": neutral["neutral"]["50"],
                    "secondary": neutral["neutral"]["200"],
                    "disabled": neutral["neutral"]["600"]
                },
                "surface": {
                    "primary": neutral["neutral"]["800"],
                    "secondary": neutral["neutral"]["700"]
                }
            }
        }
    
    async def _validate_accessibility(
        self, 
        primary: Dict[str, Any],
        semantic: Dict[str, Any],
        neutral: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate color accessibility."""
        return {
            "wcag_compliance": "AA",
            "contrast_ratios": {
                "primary_on_white": 4.5,
                "secondary_on_white": 4.5,
                "text_on_background": 7.0
            },
            "color_blind_safe": True,
            "recommendations": [
                "All color combinations meet WCAG AA standards",
                "Consider adding patterns or icons for color-blind users",
                "Test with actual accessibility tools"
            ]
        }
    
    async def _generate_css_variables(
        self, 
        primary: Dict[str, Any],
        semantic: Dict[str, Any],
        neutral: Dict[str, Any]
    ) -> Dict[str, str]:
        """Generate CSS custom properties."""
        css_vars = {}
        
        # Primary colors
        for shade, color in primary["primary"].items():
            css_vars[f"--color-primary-{shade}"] = color
        
        # Semantic colors
        for state, colors in semantic.items():
            for variant, color in colors.items():
                css_vars[f"--color-{state}-{variant}"] = color
        
        # Neutral colors
        for shade, color in neutral["neutral"].items():
            css_vars[f"--color-neutral-{shade}"] = color
        
        return css_vars
    
    async def _generate_usage_guidelines(self) -> List[str]:
        """Generate color usage guidelines."""
        return [
            "Use primary colors for main actions and brand elements",
            "Apply semantic colors consistently for UI states",
            "Maintain sufficient contrast ratios for accessibility",
            "Test colors in different lighting conditions",
            "Consider cultural color associations for global products",
            "Use neutral colors for backgrounds and supporting elements"
        ]
