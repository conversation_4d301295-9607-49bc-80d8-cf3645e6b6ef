"""Layout and Grid Designer Agent."""

from typing import Any, Dict, List
from agents.base_agent import BaseAgent, ToolDefinition, ToolParameter
from models.schemas import AgentType, AgentTask


class LayoutGridAgent(BaseAgent):
    """Agent specialized in creating responsive grid systems and layouts."""
    
    def __init__(self):
        """Initialize the Layout Grid Agent."""
        super().__init__(AgentType.LAYOUT_GRID)
    
    @property
    def name(self) -> str:
        """Get the agent name."""
        return "Layout & Grid Designer"
    
    @property
    def description(self) -> str:
        """Get the agent description."""
        return "Creates responsive grid systems and layouts for web and mobile interfaces"
    
    @property
    def capabilities(self) -> List[str]:
        """Get the agent capabilities."""
        return [
            "Responsive grid system design",
            "Layout composition and hierarchy",
            "Breakpoint definition and management",
            "Container and spacing specifications",
            "Grid alignment and positioning",
            "Mobile-first layout strategies",
            "Flexbox and CSS Grid implementations",
            "Layout accessibility considerations"
        ]
    
    def _setup_tools(self) -> None:
        """Setup agent-specific tools."""
        # Grid System Creation Tool
        grid_tool = self.create_tool_definition(
            name="create_grid_system",
            description="Create a responsive grid system with breakpoints and specifications",
            parameters=[
                self.create_tool_parameter(
                    "columns", "integer", "Number of grid columns", True
                ),
                self.create_tool_parameter(
                    "gutter_width", "string", "Gutter width specification", True
                ),
                self.create_tool_parameter(
                    "breakpoints", "array", "Responsive breakpoints", True
                ),
                self.create_tool_parameter(
                    "container_max_width", "string", "Maximum container width", True
                )
            ]
        )
        
        # Layout Composition Tool
        layout_tool = self.create_tool_definition(
            name="design_layout_composition",
            description="Design layout composition with sections and hierarchy",
            parameters=[
                self.create_tool_parameter(
                    "layout_type", "string", "Type of layout", True,
                    enum=["header-content-footer", "sidebar-main", "grid-cards", "hero-sections"]
                ),
                self.create_tool_parameter(
                    "sections", "array", "Layout sections and their properties", True
                ),
                self.create_tool_parameter(
                    "hierarchy", "string", "Visual hierarchy specification", True
                )
            ]
        )
        
        # Responsive Strategy Tool
        responsive_tool = self.create_tool_definition(
            name="define_responsive_strategy",
            description="Define responsive design strategy and behavior",
            parameters=[
                self.create_tool_parameter(
                    "mobile_first", "boolean", "Use mobile-first approach", True
                ),
                self.create_tool_parameter(
                    "breakpoint_behavior", "object", "Behavior at each breakpoint", True
                ),
                self.create_tool_parameter(
                    "content_reflow", "string", "Content reflow strategy", True
                )
            ]
        )
        
        self.add_tool(grid_tool)
        self.add_tool(layout_tool)
        self.add_tool(responsive_tool)
    
    async def _execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """Execute the layout and grid design task."""
        prompt = task.prompt
        parameters = task.parameters
        
        # Analyze the layout requirements
        layout_analysis = await self._analyze_layout_requirements(prompt, parameters)
        
        # Create grid system
        grid_system = await self._create_grid_system(layout_analysis)
        
        # Design layout composition
        layout_composition = await self._design_layout_composition(layout_analysis)
        
        # Define responsive strategy
        responsive_strategy = await self._define_responsive_strategy(layout_analysis)
        
        # Generate CSS specifications
        css_specifications = await self._generate_css_specifications(
            grid_system, layout_composition, responsive_strategy
        )
        
        return {
            "layout_analysis": layout_analysis,
            "grid_system": grid_system,
            "layout_composition": layout_composition,
            "responsive_strategy": responsive_strategy,
            "css_specifications": css_specifications,
            "implementation_notes": await self._generate_implementation_notes()
        }
    
    async def _analyze_layout_requirements(
        self, prompt: str, parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze layout requirements from the prompt."""
        return {
            "layout_type": parameters.get("layout_type", "header-content-footer"),
            "target_devices": parameters.get("target_devices", ["desktop", "tablet", "mobile"]),
            "content_types": parameters.get("content_types", ["text", "images", "forms"]),
            "complexity": "medium",
            "accessibility_requirements": True
        }
    
    async def _create_grid_system(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create a responsive grid system."""
        return {
            "columns": 12,
            "gutter_width": "24px",
            "container_max_width": "1200px",
            "breakpoints": {
                "xs": "0px",
                "sm": "576px", 
                "md": "768px",
                "lg": "992px",
                "xl": "1200px",
                "xxl": "1400px"
            },
            "margins": {
                "xs": "16px",
                "sm": "24px",
                "md": "32px",
                "lg": "48px",
                "xl": "64px"
            }
        }
    
    async def _design_layout_composition(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Design the layout composition."""
        return {
            "sections": [
                {
                    "name": "header",
                    "height": "80px",
                    "position": "sticky",
                    "z_index": 100
                },
                {
                    "name": "main_content",
                    "min_height": "calc(100vh - 160px)",
                    "padding": "32px 0"
                },
                {
                    "name": "footer",
                    "height": "80px",
                    "background": "var(--color-background-secondary)"
                }
            ],
            "hierarchy": {
                "primary": "main_content",
                "secondary": "header",
                "tertiary": "footer"
            }
        }
    
    async def _define_responsive_strategy(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Define responsive design strategy."""
        return {
            "mobile_first": True,
            "breakpoint_behavior": {
                "xs": {"columns": 1, "layout": "stacked"},
                "sm": {"columns": 2, "layout": "stacked"},
                "md": {"columns": 3, "layout": "mixed"},
                "lg": {"columns": 4, "layout": "grid"},
                "xl": {"columns": 4, "layout": "grid"}
            },
            "content_reflow": "progressive_enhancement",
            "touch_targets": "44px_minimum"
        }
    
    async def _generate_css_specifications(
        self, 
        grid_system: Dict[str, Any],
        layout_composition: Dict[str, Any],
        responsive_strategy: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate CSS specifications for the layout."""
        return {
            "css_custom_properties": {
                "--grid-columns": str(grid_system["columns"]),
                "--grid-gutter": grid_system["gutter_width"],
                "--container-max-width": grid_system["container_max_width"]
            },
            "css_classes": [
                ".container", ".row", ".col", ".col-auto",
                ".d-flex", ".justify-content-center", ".align-items-center"
            ],
            "media_queries": [
                f"@media (min-width: {bp})" for bp in grid_system["breakpoints"].values()
            ]
        }
    
    async def _generate_implementation_notes(self) -> List[str]:
        """Generate implementation notes for developers."""
        return [
            "Use CSS Grid for main layout structure",
            "Implement Flexbox for component-level layouts",
            "Ensure touch targets meet accessibility standards",
            "Test layout on actual devices, not just browser dev tools",
            "Consider content loading states in layout design",
            "Implement proper focus management for keyboard navigation"
        ]
