"""Google Gemini LLM integration service."""

import asyncio
import json
from typing import Any, Dict, List, Optional
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

from config.settings import settings
from models.schemas import AgentType


class GeminiService:
    """Service for interacting with Google Gemini LLM."""
    
    def __init__(self):
        """Initialize the Gemini service."""
        self.model = None
        self._setup_client()
    
    def _setup_client(self) -> None:
        """Setup the Gemini client."""
        try:
            genai.configure(api_key=settings.gemini_api_key)
            
            # Configure safety settings
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
            
            # Initialize the model
            self.model = genai.GenerativeModel(
                model_name="gemini-1.5-flash",
                safety_settings=safety_settings
            )
            
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Gemini service: {e}")
    
    async def analyze_prompt(self, prompt: str) -> Dict[str, Any]:
        """Analyze a design prompt and determine required agents."""
        analysis_prompt = f"""
        Analyze the following design prompt and determine which specialized design agents are needed.
        
        Design Prompt: "{prompt}"
        
        Available Agent Types:
        - layout_grid_designer: Creates responsive grid systems and layouts
        - component_creator: Builds reusable UI components
        - design_token_manager: Manages design system tokens and variables
        - typography_specialist: Handles font selection, sizing, and hierarchy
        - color_palette_generator: Creates cohesive color schemes
        - icon_asset_manager: Manages iconography and visual assets
        - spacing_alignment_controller: Ensures consistent spacing patterns
        - border_shadow_stylist: Applies visual depth and boundaries
        - accessibility_auditor: Validates WCAG compliance and accessibility
        - responsive_design_optimizer: Ensures cross-device compatibility
        - animation_transition_designer: Creates smooth UI animations
        - prototyping_specialist: Builds interactive prototypes
        - design_system_governance: Enforces design system consistency
        - brand_guidelines_enforcer: Maintains brand identity compliance
        - user_experience_validator: Validates UX best practices
        - visual_hierarchy_optimizer: Optimizes information architecture
        - export_asset_optimizer: Optimizes assets for production
        - code_generation_assistant: Generates implementation code
        - design_development_bridge: Facilitates designer-developer handoff
        - version_control_manager: Manages design version history
        - quality_assurance_tester: Validates design quality standards
        - performance_analyzer: Analyzes design performance impact
        - cross_platform_adapter: Adapts designs for multiple platforms
        - localization_specialist: Handles internationalization requirements
        - project_manager: Orchestrates project workflows
        - feedback_collector: Gathers and processes stakeholder feedback
        - documentation_generator: Creates design documentation
        - stakeholder_communicator: Manages communication workflows
        - design_trend_analyzer: Analyzes current design trends
        - error_handler_recovery: Manages system errors and recovery
        - channel_connector: Prepares MCP integration endpoints
        
        Return a JSON response with:
        1. "required_agents": List of agent types needed (use exact agent type names)
        2. "task_breakdown": Object mapping each agent to specific tasks
        3. "priority_order": List of agents in execution priority order
        4. "estimated_complexity": "low", "medium", or "high"
        5. "design_category": Category of the design request
        
        Respond only with valid JSON.
        """
        
        try:
            response = await asyncio.to_thread(
                self.model.generate_content, 
                analysis_prompt
            )
            
            # Parse the JSON response
            result = json.loads(response.text.strip())
            
            # Validate the response structure
            if not all(key in result for key in [
                "required_agents", "task_breakdown", "priority_order", 
                "estimated_complexity", "design_category"
            ]):
                raise ValueError("Invalid response structure from Gemini")
            
            return result
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse Gemini response as JSON: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to analyze prompt with Gemini: {e}")
    
    async def generate_agent_task(
        self, 
        agent_type: AgentType, 
        original_prompt: str, 
        specific_task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate specific task parameters for an agent."""
        task_prompt = f"""
        Generate specific task parameters for a {agent_type.value} agent.
        
        Original Design Prompt: "{original_prompt}"
        Specific Task: "{specific_task}"
        Context: {json.dumps(context or {}, indent=2)}
        
        Create detailed task parameters that the {agent_type.value} can execute.
        Consider the agent's specific capabilities and the overall design goal.
        
        Return a JSON response with:
        1. "task_description": Detailed description of what the agent should do
        2. "parameters": Object with specific parameters for the agent
        3. "success_criteria": List of criteria to evaluate task completion
        4. "dependencies": List of other agents this task depends on
        5. "output_format": Expected format of the agent's output
        
        Respond only with valid JSON.
        """
        
        try:
            response = await asyncio.to_thread(
                self.model.generate_content, 
                task_prompt
            )
            
            result = json.loads(response.text.strip())
            
            # Validate the response structure
            required_keys = [
                "task_description", "parameters", "success_criteria", 
                "dependencies", "output_format"
            ]
            if not all(key in result for key in required_keys):
                raise ValueError("Invalid task generation response structure")
            
            return result
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse task generation response: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to generate agent task: {e}")
    
    async def consolidate_results(
        self, 
        original_prompt: str, 
        agent_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Consolidate results from multiple agents."""
        consolidation_prompt = f"""
        Consolidate the results from multiple design agents into a unified design solution.
        
        Original Design Prompt: "{original_prompt}"
        
        Agent Results:
        {json.dumps(agent_results, indent=2)}
        
        Create a consolidated design solution that:
        1. Integrates all agent outputs coherently
        2. Resolves any conflicts between agents
        3. Provides a complete design specification
        4. Includes implementation guidance
        
        Return a JSON response with:
        1. "consolidated_design": Complete design specification
        2. "implementation_plan": Step-by-step implementation guide
        3. "design_assets": List of assets and components created
        4. "quality_metrics": Quality assessment of the design
        5. "recommendations": Additional recommendations for improvement
        
        Respond only with valid JSON.
        """
        
        try:
            response = await asyncio.to_thread(
                self.model.generate_content, 
                consolidation_prompt
            )
            
            result = json.loads(response.text.strip())
            
            # Validate the response structure
            required_keys = [
                "consolidated_design", "implementation_plan", "design_assets",
                "quality_metrics", "recommendations"
            ]
            if not all(key in result for key in required_keys):
                raise ValueError("Invalid consolidation response structure")
            
            return result
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse consolidation response: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to consolidate results: {e}")


# Global Gemini service instance
gemini_service = GeminiService()
